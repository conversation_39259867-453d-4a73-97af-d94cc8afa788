# 开发工作流脚本
param(
    [string]$Action = "menu"  # setup, build, run, test, docker, clean
)

function Show-Menu {
    Write-Host "`n=== New API 开发工作流 ===" -ForegroundColor Green
    Write-Host "1. 初始化开发环境 (setup)" -ForegroundColor Cyan
    Write-Host "2. 快速构建 (build)" -ForegroundColor Cyan
    Write-Host "3. 构建并运行 (run)" -ForegroundColor Cyan
    Write-Host "4. 运行测试 (test)" -ForegroundColor Cyan
    Write-Host "5. 构建Docker镜像 (docker)" -ForegroundColor Cyan
    Write-Host "6. 清理文件 (clean)" -ForegroundColor Cyan
    Write-Host "7. 退出 (exit)" -ForegroundColor Cyan
    Write-Host ""
    $choice = Read-Host "请选择操作 (1-7)"
    
    switch ($choice) {
        "1" { Setup-Environment }
        "2" { Build-Application }
        "3" { Build-And-Run }
        "4" { Run-Tests }
        "5" { Build-Docker }
        "6" { Clean-Files }
        "7" { exit 0 }
        default { 
            Write-Host "无效选择，请重试" -ForegroundColor Red
            Show-Menu 
        }
    }
}

function Setup-Environment {
    Write-Host "`n=== 初始化开发环境 ===" -ForegroundColor Green
    .\local-dev-setup.ps1
    Pause-And-Menu
}

function Build-Application {
    Write-Host "`n=== 快速构建 ===" -ForegroundColor Green
    .\build-local.ps1 -Verbose
    Pause-And-Menu
}

function Build-And-Run {
    Write-Host "`n=== 构建并运行 ===" -ForegroundColor Green
    .\build-local.ps1 -Run
    Pause-And-Menu
}

function Run-Tests {
    Write-Host "`n=== 运行测试 ===" -ForegroundColor Green
    
    # 检查是否有测试文件
    $testFiles = Get-ChildItem "*_test.go" -Recurse
    if ($testFiles) {
        Write-Host "发现测试文件: $($testFiles.Count) 个" -ForegroundColor Cyan
        try {
            go test ./...
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✓ 所有测试通过" -ForegroundColor Green
            } else {
                Write-Host "✗ 部分测试失败" -ForegroundColor Red
            }
        } catch {
            Write-Host "✗ 测试运行出错: $_" -ForegroundColor Red
        }
    } else {
        Write-Host "没有发现测试文件" -ForegroundColor Yellow
        Write-Host "建议创建测试文件来验证功能" -ForegroundColor Cyan
    }
    
    # 运行基本功能测试
    Write-Host "`n运行基本功能测试..." -ForegroundColor Yellow
    if (Test-Path "new-api.exe") {
        Write-Host "启动应用进行功能测试..." -ForegroundColor Cyan
        $process = Start-Process ".\new-api.exe" -PassThru
        Start-Sleep 3
        
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:3000" -TimeoutSec 5 -ErrorAction Stop
            Write-Host "✓ 应用响应正常 (状态码: $($response.StatusCode))" -ForegroundColor Green
        } catch {
            Write-Host "⚠️ 应用可能未完全启动或端口被占用" -ForegroundColor Yellow
        }
        
        # 停止测试进程
        if (!$process.HasExited) {
            $process.Kill()
            Write-Host "测试完成，已停止应用" -ForegroundColor Cyan
        }
    } else {
        Write-Host "请先构建应用" -ForegroundColor Yellow
    }
    
    Pause-And-Menu
}

function Build-Docker {
    Write-Host "`n=== 构建Docker镜像 ===" -ForegroundColor Green
    
    # 检查本地构建是否成功
    if (!(Test-Path "new-api.exe")) {
        Write-Host "建议先进行本地构建测试..." -ForegroundColor Yellow
        $response = Read-Host "是否先运行本地构建? (y/n)"
        if ($response -eq 'y' -or $response -eq 'Y') {
            .\build-local.ps1
        }
    }
    
    Write-Host "准备Docker构建环境..." -ForegroundColor Yellow
    
    # 确保web/dist存在
    if (!(Test-Path "web/dist")) {
        New-Item -ItemType Directory -Path "web/dist" -Force | Out-Null
    }
    if (!(Test-Path "web/dist/index.html")) {
        '<html><head><title>New API</title></head><body><h1>New API</h1></body></html>' | Out-File -FilePath "web/dist/index.html" -Encoding UTF8
    }
    
    Write-Host "选择Docker构建方式:" -ForegroundColor Cyan
    Write-Host "1. 简化构建 (跳过复杂检查)" -ForegroundColor White
    Write-Host "2. 标准构建" -ForegroundColor White
    Write-Host "3. 调试构建 (显示详细信息)" -ForegroundColor White
    
    $dockerChoice = Read-Host "请选择 (1-3)"
    
    switch ($dockerChoice) {
        "1" {
            Write-Host "使用简化Docker构建..." -ForegroundColor Yellow
            docker build -f Dockerfile.no-vet -t new-api:latest . --no-cache
        }
        "2" {
            Write-Host "使用标准Docker构建..." -ForegroundColor Yellow
            docker build -t new-api:latest .
        }
        "3" {
            Write-Host "使用调试Docker构建..." -ForegroundColor Yellow
            docker build -f Dockerfile.show-errors -t new-api:debug . --progress=plain --no-cache
        }
        default {
            Write-Host "无效选择，使用简化构建" -ForegroundColor Yellow
            docker build -f Dockerfile.no-vet -t new-api:latest . --no-cache
        }
    }
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Docker构建成功!" -ForegroundColor Green
        Write-Host "运行容器: docker run -p 3000:3000 new-api:latest" -ForegroundColor Cyan
    } else {
        Write-Host "✗ Docker构建失败" -ForegroundColor Red
        Write-Host "建议先解决本地构建问题" -ForegroundColor Yellow
    }
    
    Pause-And-Menu
}

function Clean-Files {
    Write-Host "`n=== 清理文件 ===" -ForegroundColor Green
    
    Write-Host "清理构建文件..." -ForegroundColor Yellow
    Remove-Item "*.exe" -ErrorAction SilentlyContinue
    Remove-Item "new-api" -ErrorAction SilentlyContinue
    
    Write-Host "清理Go缓存..." -ForegroundColor Yellow
    go clean -cache -modcache -testcache
    
    Write-Host "清理Docker资源..." -ForegroundColor Yellow
    docker system prune -f
    
    Write-Host "✓ 清理完成" -ForegroundColor Green
    Pause-And-Menu
}

function Pause-And-Menu {
    Write-Host "`n按任意键返回菜单..." -ForegroundColor Gray
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    Show-Menu
}

# 主逻辑
switch ($Action.ToLower()) {
    "setup" { Setup-Environment }
    "build" { Build-Application }
    "run" { Build-And-Run }
    "test" { Run-Tests }
    "docker" { Build-Docker }
    "clean" { Clean-Files }
    "menu" { Show-Menu }
    default { Show-Menu }
}
