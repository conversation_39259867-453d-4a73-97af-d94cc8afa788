<?php
/**
 * 禁封提示页面
 */
require_once '../config/app.php';

// 获取禁封信息
$ban_type_param = $_GET['type'] ?? 'user'; // user 或 ip
$ban_type = $_GET['ban_type'] ?? 'temporary'; // temporary 或 permanent
$ban_reason = urldecode($_GET['reason'] ?? '');
$expires_at = urldecode($_GET['expires_at'] ?? '');
$banned_by = urldecode($_GET['banned_by'] ?? '');
$ip_address = urldecode($_GET['ip'] ?? '');
$from_page = $_GET['from'] ?? 'login'; // login 或 distribution

// 验证参数
if (empty($ban_reason)) {
    header('Location: ../index.php');
    exit;
}

// 设置返回链接
$return_url = '../index.php';
$return_text = '返回首页';

if ($from_page === 'distribution') {
    $return_text = '返回分发页面';
    // 可以根据需要设置具体的分发页面URL
}

// 格式化到期时间
$expires_text = '';
if ($ban_type === 'temporary' && $expires_at) {
    $expires_timestamp = strtotime($expires_at);
    $now = time();
    
    if ($expires_timestamp > $now) {
        $time_diff = $expires_timestamp - $now;
        $hours = floor($time_diff / 3600);
        $minutes = floor(($time_diff % 3600) / 60);
        
        if ($hours > 0) {
            $expires_text = "约 {$hours} 小时 {$minutes} 分钟后自动解封";
        } else {
            $expires_text = "约 {$minutes} 分钟后自动解封";
        }
        
        $expires_date = date('Y年m月d日 H:i', $expires_timestamp);
        $expires_text .= "<br><small class='text-muted'>具体时间：{$expires_date}</small>";
    } else {
        $expires_text = "禁封已到期，请重新尝试登录";
    }
}

// 设置页面标题和图标
$page_title = $ban_type_param === 'ip' ? 'IP地址被禁封' : '账户被禁封';
$ban_icon = $ban_type_param === 'ip' ? 'bi-shield-x' : 'bi-person-x';
$ban_target = $ban_type_param === 'ip' ? "IP地址 {$ip_address}" : '您的账户';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Microsoft YaHei', sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .ban-container {
            max-width: 600px;
            width: 100%;
            margin: 20px;
        }
        .ban-card {
            background: rgba(255, 255, 255, 0.95);
            border: none;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            overflow: hidden;
        }
        .ban-header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .ban-icon {
            font-size: 4rem;
            margin-bottom: 15px;
            opacity: 0.9;
        }
        .ban-body {
            padding: 40px;
        }
        .ban-reason {
            background: #f8f9fa;
            border-left: 4px solid #dc3545;
            padding: 15px 20px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }
        .ban-details {
            background: #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .ban-details .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #dee2e6;
        }
        .ban-details .detail-item:last-child {
            border-bottom: none;
        }
        .ban-details .detail-label {
            font-weight: 600;
            color: #495057;
        }
        .ban-details .detail-value {
            color: #6c757d;
            text-align: right;
        }
        .btn-home {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            color: white;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        .btn-home:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }
        .countdown {
            font-size: 1.1rem;
            font-weight: 600;
            color: #28a745;
        }
        .permanent-ban {
            color: #dc3545;
            font-weight: 600;
        }
        .contact-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }
        .contact-info h6 {
            color: #0c5460;
            margin-bottom: 10px;
        }
        .contact-info p {
            color: #0c5460;
            margin: 0;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="ban-container">
        <div class="card ban-card">
            <div class="ban-header">
                <i class="bi <?php echo $ban_icon; ?> ban-icon"></i>
                <h2 class="mb-0"><?php echo $page_title; ?></h2>
                <p class="mb-0 mt-2">访问受限</p>
            </div>
            
            <div class="ban-body">
                <div class="text-center mb-4">
                    <h4 class="text-danger">抱歉，<?php echo $ban_target; ?>已被禁封</h4>
                    <p class="text-muted">您当前无法访问本站点的相关功能</p>
                </div>
                
                <div class="ban-reason">
                    <h6 class="text-danger mb-2">
                        <i class="bi bi-exclamation-triangle me-2"></i>禁封原因
                    </h6>
                    <p class="mb-0"><?php echo nl2br(htmlspecialchars($ban_reason)); ?></p>
                </div>
                
                <div class="ban-details">
                    <h6 class="mb-3">
                        <i class="bi bi-info-circle me-2"></i>详细信息
                    </h6>
                    
                    <div class="detail-item">
                        <span class="detail-label">禁封类型</span>
                        <span class="detail-value">
                            <?php if ($ban_type === 'permanent'): ?>
                                <span class="permanent-ban">永久禁封</span>
                            <?php else: ?>
                                <span class="text-warning">临时禁封</span>
                            <?php endif; ?>
                        </span>
                    </div>
                    
                    <?php if ($ban_type_param === 'ip'): ?>
                        <div class="detail-item">
                            <span class="detail-label">被禁封的IP</span>
                            <span class="detail-value">
                                <code><?php echo htmlspecialchars($ip_address); ?></code>
                            </span>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($banned_by): ?>
                        <div class="detail-item">
                            <span class="detail-label">执行者</span>
                            <span class="detail-value"><?php echo htmlspecialchars($banned_by); ?></span>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($ban_type === 'temporary'): ?>
                        <div class="detail-item">
                            <span class="detail-label">解封状态</span>
                            <span class="detail-value countdown">
                                <?php echo $expires_text; ?>
                            </span>
                        </div>
                    <?php endif; ?>
                </div>
                
                <?php if ($ban_type === 'temporary' && $expires_at): ?>
                    <div class="alert alert-info">
                        <i class="bi bi-clock me-2"></i>
                        <strong>临时禁封说明：</strong>
                        此禁封为临时性质，将在指定时间后自动解除。届时您可以重新尝试登录。
                    </div>
                <?php else: ?>
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <strong>永久禁封说明：</strong>
                        此禁封为永久性质，如有疑问请联系管理员。
                    </div>
                <?php endif; ?>
                
                <div class="contact-info">
                    <h6>
                        <i class="bi bi-envelope me-2"></i>需要帮助？
                    </h6>
                    <p>如果您认为此禁封有误，或需要申诉，请联系网站管理员。</p>
                    <p>在联系时，请提供您的用户信息和此页面的详细信息。</p>
                </div>
                
                <div class="text-center mt-4">
                    <a href="<?php echo $return_url; ?>" class="btn-home">
                        <i class="bi bi-house me-2"></i><?php echo $return_text; ?>
                    </a>
                    <?php if ($from_page === 'distribution'): ?>
                        <a href="../index.php" class="btn-home ms-2" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                            <i class="bi bi-list me-2"></i>查看所有分发
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <?php if ($ban_type === 'temporary' && $expires_at): ?>
    <script>
        // 倒计时功能
        function updateCountdown() {
            const expiresAt = new Date('<?php echo date('c', strtotime($expires_at)); ?>').getTime();
            const now = new Date().getTime();
            const timeLeft = expiresAt - now;
            
            if (timeLeft > 0) {
                const hours = Math.floor(timeLeft / (1000 * 60 * 60));
                const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);
                
                let countdownText = '';
                if (hours > 0) {
                    countdownText = `约 ${hours} 小时 ${minutes} 分钟 ${seconds} 秒后自动解封`;
                } else if (minutes > 0) {
                    countdownText = `约 ${minutes} 分钟 ${seconds} 秒后自动解封`;
                } else {
                    countdownText = `约 ${seconds} 秒后自动解封`;
                }
                
                const countdownElements = document.querySelectorAll('.countdown');
                countdownElements.forEach(element => {
                    element.innerHTML = countdownText + '<br><small class="text-muted">具体时间：<?php echo date('Y年m月d日 H:i', strtotime($expires_at)); ?></small>';
                });
            } else {
                // 禁封已到期
                const countdownElements = document.querySelectorAll('.countdown');
                countdownElements.forEach(element => {
                    element.innerHTML = '<span class="text-success">禁封已到期，请重新尝试登录</span>';
                });
                
                // 显示重新登录按钮
                setTimeout(() => {
                    location.reload();
                }, 2000);
            }
        }
        
        // 每秒更新倒计时
        updateCountdown();
        setInterval(updateCountdown, 1000);
    </script>
    <?php endif; ?>
</body>
</html>
