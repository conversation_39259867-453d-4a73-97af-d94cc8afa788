# Backend-only Dockerfile for debugging
FROM golang:1.23-alpine AS builder

ENV GO111MODULE=on \
    CGO_ENABLED=0 \
    GOOS=linux \
    GOARCH=amd64 \
    GOPROXY=https://goproxy.cn,https://goproxy.io,direct \
    GOSUMDB=sum.golang.google.cn

WORKDIR /build

# Install necessary packages
RUN apk add --no-cache git ca-certificates tzdata

# Create minimal web/dist for Go embed
RUN mkdir -p web/dist && \
    echo '<!DOCTYPE html><html><head><title>New API</title></head><body><h1>New API Backend</h1><p>Frontend disabled for testing</p></body></html>' > web/dist/index.html

# Copy go mod files and download dependencies first
COPY go.mod go.sum ./
RUN echo "Downloading Go modules..." && \
    go mod download && \
    go mod verify && \
    echo "Go modules downloaded successfully"

# Copy all source files
COPY . .

# Ensure VERSION file exists
RUN if [ ! -s VERSION ]; then echo "v1.0.0" > VERSION; fi

# Verify build environment
RUN echo "=== Build Environment ===" && \
    echo "Go version: $(go version)" && \
    echo "GOPROXY: $GOPROXY" && \
    echo "VERSION: $(cat VERSION)" && \
    echo "Web dist files:" && ls -la web/dist/ && \
    echo "========================="

# Test Go syntax first
RUN echo "Checking Go syntax..." && \
    go vet ./... && \
    echo "Go syntax check passed"

# Build the application with verbose output
RUN echo "Building application..." && \
    go build -v -ldflags "-s -w -X 'one-api/common.Version=$(cat VERSION)'" -o one-api 2>&1 && \
    echo "Build completed successfully!"

# Verify the binary
RUN ls -la one-api && file one-api && echo "Binary verification passed!"

FROM alpine:latest

RUN apk upgrade --no-cache \
    && apk add --no-cache ca-certificates tzdata ffmpeg \
    && update-ca-certificates \
    && adduser -D -s /bin/sh oneapi

COPY --from=builder /build/one-api /usr/local/bin/one-api
RUN chmod +x /usr/local/bin/one-api

USER oneapi
EXPOSE 3000
WORKDIR /data
ENTRYPOINT ["/usr/local/bin/one-api"]
