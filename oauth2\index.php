<?php
error_reporting(0);
session_start();
@header('Content-Type: text/html; charset=UTF-8');

// 如果用户已经登录，直接重定向到主站首页
if (isset($_SESSION['user_id']) && !empty($_SESSION['user_id'])) {
    header('Location: ../index.php');
    exit;
}

// 获取用户等级标识的函数
function getUserLevelBadge($userGroups) {
    if (empty($userGroups) || !is_array($userGroups)) {
        return '';
    }

    // 定义用户组到等级标识的映射
    $levelMapping = [
        'trust_level_0' => 'NL0',
        'trust_level_1' => 'NL1',
        'trust_level_2' => 'NL2',
        'trust_level_3' => 'NL3',
        'trust_level_4' => 'NL4'
    ];

    // 查找最高等级
    $highestLevel = '';
    $highestLevelNum = -1;

    foreach ($userGroups as $group) {
        if (isset($levelMapping[$group])) {
            // 提取等级数字
            $levelNum = (int)substr($group, -1);
            if ($levelNum > $highestLevelNum) {
                $highestLevelNum = $levelNum;
                $highestLevel = $levelMapping[$group];
            }
        }
    }

    return $highestLevel;
}

?><!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="utf-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="renderer" content="webkit"/>
  <title>用户登录 - <?php
    // 尝试获取系统设置，如果失败则使用默认值
    try {
        require_once '../config/app.php';
        $site_name = getSetting('site_name', '激活码分发站');
        echo htmlspecialchars($site_name);
    } catch (Exception $e) {
        echo '激活码分发站';
    }
  ?></title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
  <style>
  body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    font-family: 'Microsoft YaHei', sans-serif;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px 0;
  }

  .login-container {
    width: 100%;
    max-width: 500px;
  }

  .card {
    border: none;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(15px);
    background: rgba(255, 255, 255, 0.95);
    overflow: hidden;
  }

  .card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 2rem 1.5rem 1.5rem;
    text-align: center;
  }

  .card-header.success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
  }

  .card-header.info {
    background: linear-gradient(135deg, #3498db 0%, #85c1e9 100%);
  }

  .card-header.warning {
    background: linear-gradient(135deg, #f39c12 0%, #f7dc6f 100%);
  }

  .card-title {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }

  .card-body {
    padding: 2rem 1.5rem;
  }

  .btn-login {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 50px;
    padding: 15px 40px;
    font-size: 1.1rem;
    font-weight: 600;
    color: white;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    text-transform: uppercase;
    letter-spacing: 1px;
  }

  .btn-login:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
    color: white;
  }

  .btn-logout {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    border: none;
    border-radius: 50px;
    padding: 12px 30px;
    font-weight: 600;
    color: white;
    transition: all 0.3s ease;
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.3);
  }

  .btn-logout:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
    color: white;
  }

  .avatar-container {
    text-align: center;
    margin-bottom: 2rem;
  }

  .avatar-img {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    border: 4px solid #fff;
    object-fit: cover;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    transition: transform 0.3s ease;
  }

  .avatar-img:hover {
    transform: scale(1.05);
  }

  .user-info {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    border-left: 4px solid #667eea;
  }

  .user-info label {
    font-weight: 600;
    color: #495057;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .user-info .form-control {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 10px 15px;
    font-weight: 500;
  }

  .verification-badge {
    font-size: 0.75rem;
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .verified {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
    color: white;
  }

  .unverified {
    background: linear-gradient(135deg, #ff6b6b 0%, #ffa8a8 100%);
    color: white;
  }

  .level-badge {
    font-size: 0.75rem;
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-left: 8px;
    display: inline-block;
  }

  .level-nl0 {
    background: linear-gradient(135deg, #6c757d 0%, #adb5bd 100%);
    color: white;
  }

  .level-nl1 {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
  }

  .level-nl2 {
    background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
    color: white;
  }

  .level-nl3 {
    background: linear-gradient(135deg, #fd7e14 0%, #ffc107 100%);
    color: white;
  }

  .level-nl4 {
    background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
    color: white;
  }

  .token-display {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px solid #dee2e6;
    border-radius: 12px;
    padding: 1rem;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    word-break: break-all;
    position: relative;
    overflow: hidden;
  }

  .token-display::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .welcome-icon {
    font-size: 4rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1rem;
  }

  .back-link {
    position: absolute;
    top: 20px;
    left: 20px;
    color: rgba(255,255,255,0.9);
    text-decoration: none;
    padding: 8px 16px;
    background: rgba(255,255,255,0.1);
    border-radius: 20px;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    font-weight: 500;
  }

  .back-link:hover {
    color: white;
    background: rgba(255,255,255,0.2);
    transform: translateY(-2px);
  }

  @media (max-width: 768px) {
    .card-body {
      padding: 1.5rem 1rem;
    }

    .avatar-img {
      width: 80px;
      height: 80px;
    }

    .btn-login {
      padding: 12px 30px;
      font-size: 1rem;
    }

    .back-link {
      position: relative;
      top: auto;
      left: auto;
      display: inline-block;
      margin-bottom: 1rem;
    }
  }
  </style>
</head>
<body>
    <!-- 返回链接 -->
    <a href="../index.php" class="back-link">
        <i class="bi bi-arrow-left me-2"></i>返回首页
    </a>

    <div class="login-container">
        <!-- 登录页面 -->
        <div class="card">
            <div class="card-header">
                <div class="welcome-icon text-center">
                    <i class="bi bi-shield-lock"></i>
                </div>
                <h3 class="card-title">欢迎登录</h3>
                <p class="mb-0 opacity-75">使用 Nodeloc 账户安全登录</p>
            </div>
            <div class="card-body text-center">
                <div class="mb-4">
                    <i class="bi bi-person-circle display-4 text-muted mb-3"></i>
                    <h5 class="mb-3">安全认证登录</h5>
                    <p class="text-muted">
                        通过 Nodeloc 官方认证系统登录<br>
                        保护您的账户安全
                    </p>
                </div>

                <form action="./connect.php" method="get">
                    <button type="submit" class="btn btn-login w-100 mb-3">
                        <i class="bi bi-box-arrow-in-right me-2"></i>
                        立即登录
                    </button>
                </form>

                <div class="text-center">
                    <small class="text-muted">
                        <i class="bi bi-shield-check me-1"></i>
                        安全的 OAuth2 认证
                    </small>
                </div>
            </div>
        </div>



    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>