# 本地开发环境设置和测试脚本
Write-Host "=== 本地开发环境设置 ===" -ForegroundColor Green

# 检查Go环境
Write-Host "`n1. 检查Go环境..." -ForegroundColor Yellow
try {
    $goVersion = go version
    Write-Host "✓ Go版本: $goVersion" -ForegroundColor Green
    
    # 检查Go环境变量
    Write-Host "Go环境变量:" -ForegroundColor Cyan
    Write-Host "  GOROOT: $(go env GOROOT)" -ForegroundColor Cyan
    Write-Host "  GOPATH: $(go env GOPATH)" -ForegroundColor Cyan
    Write-Host "  GOPROXY: $(go env GOPROXY)" -ForegroundColor Cyan
    
} catch {
    Write-Host "✗ Go未安装或不在PATH中" -ForegroundColor Red
    Write-Host "请先安装Go: https://golang.org/dl/" -ForegroundColor Yellow
    exit 1
}

# 设置Go代理（解决网络问题）
Write-Host "`n2. 设置Go代理..." -ForegroundColor Yellow
$env:GOPROXY = "https://goproxy.cn,direct"
$env:GOSUMDB = "sum.golang.google.cn"
Write-Host "✓ 已设置中国Go代理" -ForegroundColor Green

# 检查项目文件
Write-Host "`n3. 检查项目文件..." -ForegroundColor Yellow
$requiredFiles = @("go.mod", "go.sum", "main.go", "VERSION")
foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Host "✓ $file 存在" -ForegroundColor Green
    } else {
        Write-Host "✗ $file 缺失" -ForegroundColor Red
    }
}

# 准备web/dist目录（解决embed问题）
Write-Host "`n4. 准备前端资源..." -ForegroundColor Yellow
if (!(Test-Path "web/dist")) {
    New-Item -ItemType Directory -Path "web/dist" -Force | Out-Null
    Write-Host "✓ 创建了 web/dist 目录" -ForegroundColor Green
} else {
    Write-Host "✓ web/dist 目录已存在" -ForegroundColor Green
}

if (!(Test-Path "web/dist/index.html")) {
    @'
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; border-bottom: 2px solid #007acc; padding-bottom: 10px; }
        .status { padding: 15px; background: #e8f5e8; border-left: 4px solid #4caf50; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>New API 管理界面</h1>
        <div class="status">
            <strong>状态:</strong> 服务正在运行
        </div>
        <p>这是一个临时的前端界面，用于本地开发测试。</p>
        <p>API服务已启动，可以通过API接口进行访问。</p>
    </div>
</body>
</html>
'@ | Out-File -FilePath "web/dist/index.html" -Encoding UTF8
    Write-Host "✓ 创建了临时前端页面" -ForegroundColor Green
} else {
    Write-Host "✓ 前端页面已存在" -ForegroundColor Green
}

# 检查VERSION文件
Write-Host "`n5. 检查VERSION文件..." -ForegroundColor Yellow
if (Test-Path "VERSION") {
    $version = Get-Content "VERSION" -Raw
    if ([string]::IsNullOrWhiteSpace($version)) {
        "v1.0.0" | Out-File -FilePath "VERSION" -Encoding UTF8 -NoNewline
        Write-Host "✓ 修复了空的VERSION文件" -ForegroundColor Green
    } else {
        Write-Host "✓ VERSION: $($version.Trim())" -ForegroundColor Green
    }
} else {
    "v1.0.0" | Out-File -FilePath "VERSION" -Encoding UTF8 -NoNewline
    Write-Host "✓ 创建了VERSION文件" -ForegroundColor Green
}

# 下载依赖
Write-Host "`n6. 下载Go依赖..." -ForegroundColor Yellow
try {
    go mod tidy
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ 依赖下载完成" -ForegroundColor Green
    } else {
        Write-Host "⚠️ go mod tidy 有警告，但继续..." -ForegroundColor Yellow
    }
} catch {
    Write-Host "✗ 依赖下载失败: $_" -ForegroundColor Red
}

# 检查代码语法
Write-Host "`n7. 检查代码语法..." -ForegroundColor Yellow
try {
    $vetOutput = go vet ./... 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ 代码语法检查通过" -ForegroundColor Green
    } else {
        Write-Host "⚠️ 发现代码警告:" -ForegroundColor Yellow
        Write-Host $vetOutput -ForegroundColor Gray
        Write-Host "继续构建..." -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️ 语法检查跳过" -ForegroundColor Yellow
}

# 尝试编译
Write-Host "`n8. 尝试编译..." -ForegroundColor Yellow
try {
    $buildOutput = go build -o new-api.exe . 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ 编译成功!" -ForegroundColor Green
        
        # 显示文件信息
        if (Test-Path "new-api.exe") {
            $fileInfo = Get-Item "new-api.exe"
            Write-Host "  文件大小: $([math]::Round($fileInfo.Length / 1MB, 2)) MB" -ForegroundColor Cyan
            Write-Host "  创建时间: $($fileInfo.CreationTime)" -ForegroundColor Cyan
        }
        
        # 询问是否运行
        Write-Host "`n是否要启动应用进行测试? (y/n): " -ForegroundColor Yellow -NoNewline
        $response = Read-Host
        if ($response -eq 'y' -or $response -eq 'Y') {
            Write-Host "`n启动应用..." -ForegroundColor Green
            Write-Host "访问地址: http://localhost:3000" -ForegroundColor Cyan
            Write-Host "按 Ctrl+C 停止应用" -ForegroundColor Yellow
            .\new-api.exe
        }
        
    } else {
        Write-Host "✗ 编译失败:" -ForegroundColor Red
        Write-Host $buildOutput -ForegroundColor Gray
        
        Write-Host "`n尝试详细诊断..." -ForegroundColor Yellow
        # 尝试不同的编译选项
        Write-Host "测试基本编译..." -ForegroundColor Cyan
        go build . 2>&1
        
        Write-Host "测试不带ldflags编译..." -ForegroundColor Cyan
        go build -o test.exe . 2>&1
    }
} catch {
    Write-Host "✗ 编译过程出错: $_" -ForegroundColor Red
}

Write-Host "`n=== 本地开发环境设置完成 ===" -ForegroundColor Green
Write-Host "如果编译成功，你可以:" -ForegroundColor Cyan
Write-Host "1. 运行: .\new-api.exe" -ForegroundColor Cyan
Write-Host "2. 访问: http://localhost:3000" -ForegroundColor Cyan
Write-Host "3. 修改代码后重新运行此脚本" -ForegroundColor Cyan
