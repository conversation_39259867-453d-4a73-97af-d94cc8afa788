# 测试Docker构建脚本
Write-Host "=== Testing Docker Build ===" -ForegroundColor Green

# 检查Docker状态
Write-Host "Checking Docker..." -ForegroundColor Yellow
try {
    docker version | Out-Null
    Write-Host "Docker is running ✓" -ForegroundColor Green
} catch {
    Write-Host "Docker is not running ✗" -ForegroundColor Red
    exit 1
}

# 确保web/dist目录存在
Write-Host "Ensuring web/dist directory exists..." -ForegroundColor Yellow
if (!(Test-Path "web/dist")) {
    New-Item -ItemType Directory -Path "web/dist" -Force | Out-Null
}

if (!(Test-Path "web/dist/index.html")) {
    @'
<!DOCTYPE html>
<html>
<head>
    <title>New API</title>
</head>
<body>
    <h1>New API</h1>
    <p>Backend-only mode for testing</p>
</body>
</html>
'@ | Out-File -FilePath "web/dist/index.html" -Encoding UTF8
}

Write-Host "web/dist directory prepared ✓" -ForegroundColor Green

# 测试后端专用构建
Write-Host "Testing backend-only build..." -ForegroundColor Yellow
try {
    docker build -f Dockerfile.backend-only -t new-api-test:backend .
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Backend-only build successful ✓" -ForegroundColor Green
        
        # 测试运行
        Write-Host "Testing container run..." -ForegroundColor Yellow
        $containerId = docker run -d -p 3001:3000 new-api-test:backend
        Start-Sleep 3
        
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:3001" -TimeoutSec 5 -ErrorAction Stop
            Write-Host "Container is responding ✓" -ForegroundColor Green
        } catch {
            Write-Host "Container not responding (this might be normal) ⚠️" -ForegroundColor Yellow
        }
        
        # 清理
        docker stop $containerId | Out-Null
        docker rm $containerId | Out-Null
        Write-Host "Test container cleaned up ✓" -ForegroundColor Green
        
    } else {
        Write-Host "Backend-only build failed ✗" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "Error during backend build: $_" -ForegroundColor Red
    exit 1
}

# 测试完整构建
Write-Host "Testing full build..." -ForegroundColor Yellow
try {
    docker build -t new-api-test:full .
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Full build successful ✓" -ForegroundColor Green
        
        # 显示镜像信息
        Write-Host "`nBuilt images:" -ForegroundColor Yellow
        docker images new-api-test
        
    } else {
        Write-Host "Full build failed ✗" -ForegroundColor Red
        Write-Host "But backend-only build worked, so the issue is likely in frontend build" -ForegroundColor Yellow
    }
} catch {
    Write-Host "Error during full build: $_" -ForegroundColor Red
}

Write-Host "`n=== Test Complete ===" -ForegroundColor Green
