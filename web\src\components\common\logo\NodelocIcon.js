import React from 'react';
import { Icon } from '@douyinfe/semi-ui';

const NodelocIcon = (props) => {
  function CustomIcon() {
    return (
      <svg
        className='icon'
        viewBox='0 0 16 16'
        version='1.1'
        xmlns='http://www.w3.org/2000/svg'
        width='1em'
        height='1em'
        {...props}
      >
        <g id='nodeloc_icon' data-name='nodeloc_icon'>
          <path
            d='M8,0C3.6,0,0,3.6,0,8s3.6,8,8,8s8-3.6,8-8S12.4,0,8,0z'
            fill='#4CAF50'
          />
          <path
            d='M6.5,12.5L3,9l1.4-1.4l2.1,2.1l5.1-5.1L13,6L6.5,12.5z'
            fill='#FFFFFF'
          />
        </g>
      </svg>
    );
  }

  return <Icon svg={<CustomIcon />} />;
};

export default NodelocIcon;
