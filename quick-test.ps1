# Quick Docker Build Test
Write-Host "=== Quick Docker Build Test ===" -ForegroundColor Green

# 准备环境
Write-Host "Preparing environment..." -ForegroundColor Yellow
if (!(Test-Path "web/dist")) {
    New-Item -ItemType Directory -Path "web/dist" -Force | Out-Null
}
'<!DOCTYPE html><html><head><title>New API</title></head><body><h1>New API</h1></body></html>' | Out-File -FilePath "web/dist/index.html" -Encoding UTF8 -Force

# 测试简化版本
Write-Host "Testing simplified Dockerfile..." -ForegroundColor Yellow
try {
    docker build -f Dockerfile.simple-fix -t new-api:simple . --no-cache
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Simplified build successful!" -ForegroundColor Green
        
        # 测试运行
        Write-Host "Testing container..." -ForegroundColor Yellow
        $containerId = docker run -d -p 3002:3000 new-api:simple
        Start-Sleep 2
        docker logs $containerId
        docker stop $containerId | Out-Null
        docker rm $containerId | Out-Null
        
        Write-Host "✓ Container test completed" -ForegroundColor Green
    } else {
        Write-Host "✗ Simplified build failed" -ForegroundColor Red
    }
} catch {
    Write-Host "Error: $_" -ForegroundColor Red
}

Write-Host "=== Test Complete ===" -ForegroundColor Green
