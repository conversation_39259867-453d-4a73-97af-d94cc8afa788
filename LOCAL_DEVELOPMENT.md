# 本地开发指南

## 🚀 快速开始

### 方法1: 一键开发环境（推荐）
```powershell
# 运行开发工作流
.\dev-workflow.ps1
```

### 方法2: 分步执行
```powershell
# 1. 初始化开发环境
.\local-dev-setup.ps1

# 2. 快速构建
.\build-local.ps1

# 3. 构建并运行
.\build-local.ps1 -Run
```

## 📋 脚本说明

### `dev-workflow.ps1` - 主工作流脚本
交互式菜单，包含所有开发操作：
- 初始化开发环境
- 快速构建
- 构建并运行
- 运行测试
- 构建Docker镜像
- 清理文件

### `local-dev-setup.ps1` - 开发环境设置
- 检查Go环境
- 设置Go代理（解决网络问题）
- 准备前端资源（web/dist）
- 检查项目文件完整性
- 下载依赖
- 尝试编译

### `build-local.ps1` - 本地构建脚本
参数选项：
- `-Run`: 构建后直接运行
- `-Clean`: 清理后构建
- `-Verbose`: 详细输出

示例：
```powershell
.\build-local.ps1 -Run -Verbose
```

## 🔧 开发流程

### 1. 首次设置
```powershell
# 运行开发环境设置
.\local-dev-setup.ps1
```

这会：
- ✅ 检查Go环境
- ✅ 设置中国Go代理
- ✅ 创建web/dist目录和临时前端
- ✅ 检查VERSION文件
- ✅ 下载依赖
- ✅ 尝试编译

### 2. 日常开发
```powershell
# 快速构建和测试
.\build-local.ps1 -Run

# 或使用工作流菜单
.\dev-workflow.ps1
```

### 3. 测试验证
```powershell
# 运行应用
.\new-api.exe

# 访问地址
http://localhost:3000
```

### 4. Docker构建（本地验证后）
```powershell
# 通过工作流菜单选择Docker构建
.\dev-workflow.ps1
# 选择 "5. 构建Docker镜像"
```

## 🛠️ 故障排查

### Go环境问题
```powershell
# 检查Go版本
go version

# 检查Go环境
go env

# 设置代理
$env:GOPROXY="https://goproxy.cn,direct"
```

### 编译问题
```powershell
# 清理缓存
go clean -cache -modcache

# 重新下载依赖
go mod tidy

# 详细构建
.\build-local.ps1 -Verbose -Clean
```

### 前端资源问题
```powershell
# 检查web/dist目录
ls web/dist/

# 重新创建（会自动处理）
.\local-dev-setup.ps1
```

## 📁 生成的文件

### 构建产物
- `new-api.exe` - 主要可执行文件
- `new-api-basic.exe` - 基础版本（调试用）
- `new-api-version.exe` - 带版本信息（调试用）

### 前端资源
- `web/dist/index.html` - 临时前端页面

### 配置文件
- `VERSION` - 版本信息文件

## 🎯 开发建议

### 1. 代码修改后
```powershell
# 快速重新构建
.\build-local.ps1 -Clean -Run
```

### 2. 测试新功能
```powershell
# 使用工作流测试
.\dev-workflow.ps1
# 选择 "4. 运行测试"
```

### 3. 准备发布
```powershell
# 本地验证通过后构建Docker
.\dev-workflow.ps1
# 选择 "5. 构建Docker镜像"
```

## 🔄 工作流程

1. **开发阶段**: 使用 `local-dev-setup.ps1` 和 `build-local.ps1`
2. **测试阶段**: 使用 `dev-workflow.ps1` 进行全面测试
3. **部署阶段**: 本地验证通过后再构建Docker

## 📞 获取帮助

如果遇到问题：
1. 运行 `.\dev-workflow.ps1` 查看详细菜单
2. 使用 `-Verbose` 参数获取详细输出
3. 检查Go环境和网络连接
4. 清理缓存后重试

## ✅ 成功标志

- ✅ `.\new-api.exe` 文件生成
- ✅ 应用能正常启动
- ✅ 访问 http://localhost:3000 有响应
- ✅ 没有编译错误或警告
