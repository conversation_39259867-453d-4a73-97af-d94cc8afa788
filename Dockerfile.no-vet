# Dockerfile without go vet to bypass syntax issues
FROM golang:1.23.4-alpine AS builder

ENV GO111MODULE=on \
    CGO_ENABLED=0 \
    GOOS=linux \
    GOARCH=amd64 \
    GOPROXY=https://goproxy.cn,https://goproxy.io,direct \
    GOSUMDB=off

WORKDIR /build

# Install necessary packages
RUN apk add --no-cache git ca-certificates tzdata

# Create web/dist for Go embed
RUN mkdir -p web/dist && \
    echo '<!DOCTYPE html><html><head><title>New API</title></head><body><h1>New API</h1></body></html>' > web/dist/index.html

# Copy go mod files and download dependencies
COPY go.mod go.sum ./
RUN go mod download

# Copy VERSION file
COPY VERSION ./
RUN if [ ! -s VERSION ]; then echo "v1.0.0" > VERSION; fi

# Copy source files
COPY *.go ./
COPY common/ ./common/
COPY constant/ ./constant/
COPY controller/ ./controller/
COPY dto/ ./dto/
COPY middleware/ ./middleware/
COPY model/ ./model/
COPY relay/ ./relay/
COPY router/ ./router/
COPY service/ ./service/
COPY setting/ ./setting/
COPY types/ ./types/
COPY i18n/ ./i18n/

# Ensure web/dist is still there
RUN ls -la web/dist/

# Debug build step by step
RUN echo "=== Build Debug Information ===" && \
    echo "Current directory: $(pwd)" && \
    echo "Files in directory:" && ls -la && \
    echo "Go files count: $(find . -name '*.go' | wc -l)" && \
    echo "VERSION content: $(cat VERSION)" && \
    echo "web/dist check:" && ls -la web/dist/ && \
    echo "Go environment:" && go env GOPROXY GOSUMDB && \
    echo "================================"

# Test simple build first
RUN echo "Testing simple build without ldflags..." && \
    go build -o one-api-simple . 2>&1 || \
    (echo "Simple build failed, showing error:" && go build -o one-api-simple . 2>&1; exit 1)

# Test with ldflags
RUN echo "Testing build with ldflags..." && \
    go build -ldflags "-s -w -X 'one-api/common.Version=$(cat VERSION)'" -o one-api . 2>&1 || \
    (echo "Ldflags build failed, showing error:" && \
     echo "Trying without version injection..." && \
     go build -ldflags "-s -w" -o one-api . 2>&1 || \
     (echo "All builds failed, final error:" && go build -o one-api . 2>&1; exit 1))

# Verify build
RUN echo "Build completed!" && ls -la one-api && file one-api

FROM alpine:latest

RUN apk upgrade --no-cache \
    && apk add --no-cache ca-certificates tzdata ffmpeg \
    && update-ca-certificates \
    && adduser -D -s /bin/sh oneapi

COPY --from=builder /build/one-api /usr/local/bin/one-api
RUN chmod +x /usr/local/bin/one-api

USER oneapi
EXPOSE 3000
WORKDIR /data
ENTRYPOINT ["/usr/local/bin/one-api"]
