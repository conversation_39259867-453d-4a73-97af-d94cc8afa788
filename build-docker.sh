#!/bin/bash
# Docker构建脚本 - 解决网络问题
# Bash script to build Docker image with network fixes

set -e

echo "=== New API Docker Build Script ==="

# 设置Go代理环境变量
echo "Setting Go proxy environment variables..."
export GOPROXY="https://goproxy.cn,https://goproxy.io,direct"
export GOSUMDB="sum.golang.google.cn"

# 检查Docker是否运行
echo "Checking Docker status..."
if docker version >/dev/null 2>&1; then
    echo "Docker is running ✓"
else
    echo "Docker is not running or not accessible ✗"
    echo "Please start Docker and try again."
    exit 1
fi

# 检查必要文件
echo "Checking required files..."
required_files=("Dockerfile" "go.mod" "go.sum" "VERSION" "main.go")
for file in "${required_files[@]}"; do
    if [[ -f "$file" ]]; then
        echo "  $file ✓"
    else
        echo "  $file ✗"
        exit 1
    fi
done

# 检查web目录
if [[ -d "web" ]]; then
    echo "  web/ directory ✓"
else
    echo "  web/ directory ✗"
    exit 1
fi

# 构建Docker镜像
echo "Building Docker image..."
image_name="new-api:latest"

if docker build -t "$image_name" . --progress=plain; then
    echo "Docker build completed successfully! ✓"
    echo "Image name: $image_name"
    
    # 显示镜像信息
    echo ""
    echo "Image information:"
    docker images "$image_name"
    
    echo ""
    echo "To run the container:"
    echo "docker run -p 3000:3000 $image_name"
else
    echo "Docker build failed ✗"
    exit 1
fi

echo ""
echo "=== Build Complete ==="
