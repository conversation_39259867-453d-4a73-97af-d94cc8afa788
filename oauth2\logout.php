<?php
/**
 * Nodeloc登录SDK - 退出登录
 * 1.0
 * https://nodeloc.cc/u/pastking/summary
**/

session_start();

// 检查是否已登录
$was_logged_in = isset($_SESSION['user_id']);

// 清除所有与用户相关的会话数据
unset($_SESSION['user_id']);
unset($_SESSION['user_username']);
unset($_SESSION['user_name']);
unset($_SESSION['user_email']);
unset($_SESSION['user_picture']);
unset($_SESSION['user_email_verified']);
unset($_SESSION['user_groups']);
unset($_SESSION['access_token']);
unset($_SESSION['refresh_token']);
unset($_SESSION['id_token']);
unset($_SESSION['id_token_data']);
unset($_SESSION['oauth_state']);

if (!$was_logged_in) {
    // 如果用户本来就没登录，直接重定向
    header("Location: ./");
    exit();
}

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>退出登录成功 - 激活码分发站</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            font-family: 'Microsoft YaHei', sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .logout-container {
            text-align: center;
            background: rgba(255, 255, 255, 0.95);
            padding: 3rem 2rem;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(15px);
            max-width: 500px;
            width: 100%;
        }

        .success-icon {
            font-size: 4rem;
            color: #28a745;
            margin-bottom: 1.5rem;
            animation: checkmark 0.6s ease-in-out;
        }

        @keyframes checkmark {
            0% { transform: scale(0); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }

        .logout-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 1rem;
        }

        .logout-message {
            font-size: 1rem;
            color: #666;
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .countdown-container {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border: 2px solid #28a745;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            position: relative;
            overflow: hidden;
        }

        .countdown-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .countdown-text {
            font-size: 1rem;
            font-weight: 600;
            color: #155724;
            margin-bottom: 0.5rem;
        }

        .countdown-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #28a745;
            margin: 0.5rem 0;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .back-link {
            display: inline-block;
            padding: 12px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .back-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
            color: white;
            text-decoration: none;
        }

        .security-info {
            font-size: 0.9rem;
            color: #666;
            margin-top: 2rem;
            padding: 1rem;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 12px;
            border-left: 4px solid #ffc107;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="logout-container">
        <div class="success-icon">
            <i class="bi bi-check-circle-fill"></i>
        </div>

        <div class="logout-title">退出登录成功</div>

        <div class="logout-message">
            您已成功退出系统<br>
            所有会话数据已安全清除
        </div>

        <div class="countdown-container">
            <div class="countdown-text">
                <i class="bi bi-clock me-2"></i>页面将在以下秒数后自动跳转
            </div>
            <div class="countdown-number" id="countdown">3</div>
        </div>

        <div class="d-flex gap-3 justify-content-center mb-3">
            <a href="../index.php" class="back-link">
                <i class="bi bi-house me-2"></i>返回首页
            </a>
            <a href="./" class="btn btn-outline-primary" style="border-radius: 25px; padding: 12px 30px;">
                <i class="bi bi-arrow-clockwise me-2"></i>重新登录
            </a>
        </div>

        <div class="security-info">
            <i class="bi bi-shield-check me-2"></i>
            <strong>安全提示：</strong><br>
            为了您的账户安全，建议在公共设备上使用后及时退出登录，
            并关闭浏览器窗口。
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let countdown = 3;
        const countdownElement = document.getElementById('countdown');

        const timer = setInterval(function() {
            countdown--;
            countdownElement.textContent = countdown;

            if (countdown <= 0) {
                clearInterval(timer);
                window.location.href = '../index.php';
            }
        }, 1000);
    </script>
</body>
</html>