# Dockerfile that shows actual build errors
FROM golang:1.23.4-alpine AS builder

ENV GO111MODULE=on \
    CGO_ENABLED=0 \
    GOOS=linux \
    GOARCH=amd64 \
    GOPROXY=https://goproxy.cn,direct \
    GOSUMDB=off

WORKDIR /build

# Install packages
RUN apk add --no-cache git ca-certificates tzdata

# Create web/dist first
RUN mkdir -p web/dist && \
    echo '<!DOCTYPE html><html><head><title>New API</title></head><body><h1>New API</h1></body></html>' > web/dist/index.html

# Copy go files
COPY go.mod go.sum ./
RUN go mod download

COPY VERSION ./
RUN if [ ! -s VERSION ]; then echo "v1.0.0" > VERSION; fi

# Copy all source
COPY . .

# Verify structure
RUN echo "=== STRUCTURE CHECK ===" && \
    ls -la && \
    echo "web/dist contents:" && \
    ls -la web/dist/ && \
    echo "Go files count: $(find . -name '*.go' | wc -l)" && \
    echo "VERSION: $(cat VERSION)"

# Test basic compilation first
RUN echo "=== TESTING BASIC BUILD ===" && \
    go build . 2>&1 || (echo "BASIC BUILD ERROR:" && go build . 2>&1 && exit 1)

# Test with output flag
RUN echo "=== TESTING WITH OUTPUT ===" && \
    go build -o test-app . 2>&1 || (echo "OUTPUT BUILD ERROR:" && go build -o test-app . 2>&1 && exit 1)

# Test with simple ldflags
RUN echo "=== TESTING SIMPLE LDFLAGS ===" && \
    go build -ldflags "-s -w" -o test-app2 . 2>&1 || (echo "SIMPLE LDFLAGS ERROR:" && go build -ldflags "-s -w" -o test-app2 . 2>&1 && exit 1)

# Test version injection separately
RUN echo "=== TESTING VERSION INJECTION ===" && \
    echo "VERSION content for injection: '$(cat VERSION)'" && \
    go build -ldflags "-X 'one-api/common.Version=$(cat VERSION)'" -o test-app3 . 2>&1 || (echo "VERSION INJECTION ERROR:" && go build -ldflags "-X 'one-api/common.Version=$(cat VERSION)'" -o test-app3 . 2>&1 && exit 1)

# Final build with full ldflags
RUN echo "=== FINAL BUILD ===" && \
    go build -ldflags "-s -w -X 'one-api/common.Version=$(cat VERSION)'" -o one-api . 2>&1 || \
    (echo "FINAL BUILD ERROR DETAILS:" && \
     echo "Command: go build -ldflags \"-s -w -X 'one-api/common.Version=\$(cat VERSION)'\" -o one-api ." && \
     echo "VERSION content: '$(cat VERSION)'" && \
     echo "Error output:" && \
     go build -ldflags "-s -w -X 'one-api/common.Version=$(cat VERSION)'" -o one-api . 2>&1 && \
     exit 1)

RUN echo "BUILD SUCCESSFUL!" && ls -la one-api*

FROM alpine:latest
RUN apk add --no-cache ca-certificates tzdata
COPY --from=builder /build/one-api /usr/local/bin/one-api
RUN chmod +x /usr/local/bin/one-api
EXPOSE 3000
ENTRYPOINT ["/usr/local/bin/one-api"]
