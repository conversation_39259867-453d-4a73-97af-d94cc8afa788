<?php
/**
 * Nodeloc登录SDK
 * 1.0
 * https://nodeloc.cc/u/pastking/summary
**/
// ClientId 在 https://conn.nodeloc.cc/apps 获取
$ClientId = 'fc36cfe3531839e0257dbb1cfdae0a25';

// ClientSecret 在 https://conn.nodeloc.cc/apps 获取
$ClientSecret = 'defc4f6e80b0858ce4ec0697a8b9145fddebf27dde45de084c49bce9c18b6e42';

// 回调地址配置 - 必须与在 Nodeloc 应用管理中注册的地址完全一致
// 方式1: 手动设置完整的回调地址（推荐生产环境使用）
$RedirectUri = 'http://127.0.0.1:88/ffz/oauth2/return.php';  // 修正：根据nginx配置调整路径

// 方式2: 如果$RedirectUri为空，则自动构建回调地址
if (empty($RedirectUri)) {
    $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https://' : 'http://';
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    $script_dir = dirname($_SERVER['SCRIPT_NAME']);
    $RedirectUri = $protocol . $host . $script_dir . 'return.php';
}