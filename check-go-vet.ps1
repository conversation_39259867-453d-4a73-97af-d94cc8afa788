# Check Go vet issues locally
Write-Host "=== Checking Go vet issues ===" -ForegroundColor Green

# 确保web/dist存在
Write-Host "Preparing web/dist directory..." -ForegroundColor Yellow
if (!(Test-Path "web/dist")) {
    New-Item -ItemType Directory -Path "web/dist" -Force | Out-Null
}

if (!(Test-Path "web/dist/index.html")) {
    @'
<!DOCTYPE html>
<html>
<head>
    <title>New API</title>
</head>
<body>
    <h1>New API</h1>
</body>
</html>
'@ | Out-File -FilePath "web/dist/index.html" -Encoding UTF8
}

Write-Host "web/dist prepared ✓" -ForegroundColor Green

# 检查Go版本
Write-Host "`nChecking Go version..." -ForegroundColor Yellow
go version

# 检查Go环境
Write-Host "`nChecking Go environment..." -ForegroundColor Yellow
go env GOPROXY GOSUMDB

# 运行go vet检查
Write-Host "`nRunning go vet..." -ForegroundColor Yellow
try {
    $vetOutput = go vet ./... 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ go vet passed!" -ForegroundColor Green
    } else {
        Write-Host "✗ go vet failed with issues:" -ForegroundColor Red
        Write-Host $vetOutput -ForegroundColor Yellow
        
        Write-Host "`nTrying to build anyway..." -ForegroundColor Yellow
        $buildOutput = go build -o test-build.exe . 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ Build succeeded despite vet issues!" -ForegroundColor Green
            Remove-Item "test-build.exe" -ErrorAction SilentlyContinue
        } else {
            Write-Host "✗ Build also failed:" -ForegroundColor Red
            Write-Host $buildOutput -ForegroundColor Yellow
        }
    }
} catch {
    Write-Host "Error running go vet: $_" -ForegroundColor Red
}

# 测试Docker构建（跳过vet）
Write-Host "`nTesting Docker build without go vet..." -ForegroundColor Yellow
try {
    docker build -f Dockerfile.no-vet -t new-api:no-vet . --progress=plain
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Docker build without vet succeeded!" -ForegroundColor Green
        
        # 测试运行
        Write-Host "Testing container..." -ForegroundColor Yellow
        $containerId = docker run -d -p 3003:3000 new-api:no-vet
        Start-Sleep 3
        
        try {
            $null = Invoke-WebRequest -Uri "http://localhost:3003" -TimeoutSec 5 -ErrorAction Stop
            Write-Host "✓ Container is responding!" -ForegroundColor Green
        } catch {
            Write-Host "⚠️ Container not responding (may be normal)" -ForegroundColor Yellow
        }
        
        # 清理
        docker stop $containerId | Out-Null
        docker rm $containerId | Out-Null
        Write-Host "Container test completed" -ForegroundColor Cyan
        
    } else {
        Write-Host "✗ Docker build failed even without vet" -ForegroundColor Red
    }
} catch {
    Write-Host "Error during Docker build: $_" -ForegroundColor Red
}

Write-Host "`n=== Analysis Complete ===" -ForegroundColor Green
Write-Host "If go vet failed but build succeeded, you can use Dockerfile.no-vet" -ForegroundColor Cyan
Write-Host "If both failed, there are more serious code issues to fix" -ForegroundColor Cyan
