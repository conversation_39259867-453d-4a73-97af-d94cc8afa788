# Test the embed fix
Write-Host "=== Testing Embed Fix ===" -ForegroundColor Green

# 准备环境
Write-Host "Preparing environment..." -ForegroundColor Yellow
if (!(Test-Path "web/dist")) {
    New-Item -ItemType Directory -Path "web/dist" -Force | Out-Null
}

@'
<!DOCTYPE html>
<html>
<head>
    <title>New API</title>
    <meta charset="UTF-8">
</head>
<body>
    <h1>New API</h1>
    <p>Application is running successfully</p>
</body>
</html>
'@ | Out-File -FilePath "web/dist/index.html" -Encoding UTF8 -Force

Write-Host "Environment prepared ✓" -ForegroundColor Green

# 测试embed修复版本
Write-Host "`nTesting embed fix Dockerfile..." -ForegroundColor Yellow
try {
    docker build -f Dockerfile.embed-fix -t new-api:embed-fix . --progress=plain --no-cache
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "`n🎉 EMBED FIX BUILD SUCCESSFUL!" -ForegroundColor Green
        
        # 测试运行
        Write-Host "Testing container..." -ForegroundColor Cyan
        $containerId = docker run -d -p 3000:3000 --name new-api-embed-test new-api:embed-fix
        Start-Sleep 3
        
        Write-Host "Container logs:" -ForegroundColor Cyan
        docker logs new-api-embed-test
        
        # 尝试访问
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:3000" -TimeoutSec 5 -ErrorAction Stop
            Write-Host "✓ Application is responding!" -ForegroundColor Green
            Write-Host "Response status: $($response.StatusCode)" -ForegroundColor Cyan
        } catch {
            Write-Host "⚠️ Application not responding yet (may need more time)" -ForegroundColor Yellow
        }
        
        # 清理
        docker stop new-api-embed-test | Out-Null
        docker rm new-api-embed-test | Out-Null
        
        Write-Host "`n✅ SUCCESS! Use this command to run:" -ForegroundColor Green
        Write-Host "docker run -p 3000:3000 new-api:embed-fix" -ForegroundColor Cyan
        
    } else {
        Write-Host "`n❌ Embed fix build failed" -ForegroundColor Red
        Write-Host "Running diagnostic build..." -ForegroundColor Yellow
        
        # 运行诊断
        .\diagnose-build.ps1
    }
    
} catch {
    Write-Host "Error during build: $_" -ForegroundColor Red
}

Write-Host "`n=== Test Complete ===" -ForegroundColor Green
