# Git and development files
.github
.git
.gitignore
.vscode
.idea

# Documentation
*.md
docs/

# Build and development files
Makefile
makefile
*.log
*.tmp
.env
.env.local
.env.example

# Test and build artifacts
test-build*
*.exe
*.test
coverage.out

# Node.js (if any)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Docker files (avoid recursion)
Dockerfile.*
docker-compose*.yml
*.ps1
*.sh

# Temporary web dist (will be built in container)
web/dist/
web/node_modules/