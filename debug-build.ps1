# Debug Docker Build Script
Write-Host "=== Docker Build Debug Script ===" -ForegroundColor Green

# 确保必要文件存在
Write-Host "Preparing debug environment..." -ForegroundColor Yellow

# 创建web/dist目录
if (!(Test-Path "web/dist")) {
    New-Item -ItemType Directory -Path "web/dist" -Force | Out-Null
    Write-Host "Created web/dist directory" -ForegroundColor Cyan
}

# 创建基本的index.html
$indexContent = @'
<!DOCTYPE html>
<html>
<head>
    <title>New API Debug</title>
    <meta charset="UTF-8">
</head>
<body>
    <h1>New API - Debug Mode</h1>
    <p>This is a debug build</p>
</body>
</html>
'@

$indexContent | Out-File -FilePath "web/dist/index.html" -Encoding UTF8 -Force
Write-Host "Created web/dist/index.html" -ForegroundColor Cyan

# 检查关键文件
$requiredFiles = @("go.mod", "go.sum", "main.go", "VERSION")
foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Host "✓ $file exists" -ForegroundColor Green
    } else {
        Write-Host "✗ $file missing" -ForegroundColor Red
        exit 1
    }
}

# 运行调试构建
Write-Host "`nStarting debug build..." -ForegroundColor Yellow
Write-Host "This will show detailed output to identify the exact issue." -ForegroundColor Cyan

try {
    # 使用调试Dockerfile
    Write-Host "Building with debug Dockerfile..." -ForegroundColor Yellow
    docker build -f Dockerfile.debug -t new-api:debug . --progress=plain --no-cache
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "`n✓ Debug build successful!" -ForegroundColor Green
        Write-Host "Now trying the main Dockerfile..." -ForegroundColor Yellow
        
        # 尝试主Dockerfile
        docker build -t new-api:main . --progress=plain
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ Main build also successful!" -ForegroundColor Green
        } else {
            Write-Host "✗ Main build failed, but debug build worked" -ForegroundColor Yellow
            Write-Host "The issue is likely in the main Dockerfile structure" -ForegroundColor Yellow
        }
    } else {
        Write-Host "`n✗ Debug build failed" -ForegroundColor Red
        Write-Host "Check the output above for specific error details" -ForegroundColor Yellow
    }
} catch {
    Write-Host "Error during build: $_" -ForegroundColor Red
    exit 1
}

Write-Host "`n=== Debug Complete ===" -ForegroundColor Green
