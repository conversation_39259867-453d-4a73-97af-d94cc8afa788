# Simple Dockerfile for testing Go build issues
FROM golang:1.23-alpine AS builder

ENV GO111MODULE=on \
    CGO_ENABLED=0 \
    GOOS=linux

WORKDIR /build

# Install necessary packages
RUN apk add --no-cache git ca-certificates

# Copy go mod files and download dependencies
COPY go.mod go.sum ./
RUN go mod download

# Copy source files
COPY . .

# Create a simple web/dist directory for testing
RUN mkdir -p web/dist && echo '<!DOCTYPE html><html><head><title>Test</title></head><body><h1>Test</h1></body></html>' > web/dist/index.html

# Ensure VERSION file exists
RUN if [ ! -s VERSION ]; then echo "v1.0.0" > VERSION; fi

# Build with verbose output to see what fails
RUN go build -v -ldflags "-s -w -X 'one-api/common.Version=$(cat VERSION)'" -o one-api

FROM alpine:latest
RUN apk add --no-cache ca-certificates tzdata
COPY --from=builder /build/one-api /usr/local/bin/one-api
RUN chmod +x /usr/local/bin/one-api
EXPOSE 3000
ENTRYPOINT ["/usr/local/bin/one-api"]
