<?php
/**
 * OAuth2用户信息查看页面
 */
error_reporting(0);
session_start();
@header('Content-Type: text/html; charset=UTF-8');

// 如果用户未登录，重定向到登录页面
if (!isset($_SESSION['user_id']) || empty($_SESSION['user_id'])) {
    header('Location: ./index.php');
    exit;
}

// 获取用户等级标识的函数
function getUserLevelBadge($userGroups) {
    if (empty($userGroups) || !is_array($userGroups)) {
        return '';
    }

    // 定义用户组到等级标识的映射
    $levelMapping = [
        'trust_level_0' => 'NL0',
        'trust_level_1' => 'NL1',
        'trust_level_2' => 'NL2',
        'trust_level_3' => 'NL3',
        'trust_level_4' => 'NL4'
    ];

    // 查找最高等级
    $highestLevel = '';
    $highestLevelNum = -1;

    foreach ($userGroups as $group) {
        if (isset($levelMapping[$group])) {
            // 提取等级数字
            $levelNum = (int)substr($group, -1);
            if ($levelNum > $highestLevelNum) {
                $highestLevelNum = $levelNum;
                $highestLevel = $levelMapping[$group];
            }
        }
    }

    return $highestLevel;
}

?><!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit"/>
    <title>用户信息 - OAuth2</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Microsoft YaHei', sans-serif;
            padding: 20px 0;
        }

        .main-container {
            max-width: 1000px;
            margin: 0 auto;
        }

        .card {
            border: none;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(15px);
            background: rgba(255, 255, 255, 0.95);
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 1.5rem;
        }

        .card-header.success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }

        .card-header.info {
            background: linear-gradient(135deg, #3498db 0%, #85c1e9 100%);
        }

        .card-header.warning {
            background: linear-gradient(135deg, #f39c12 0%, #f7dc6f 100%);
        }

        .avatar-container {
            text-align: center;
            margin-bottom: 2rem;
        }

        .avatar-img {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            border: 4px solid #fff;
            object-fit: cover;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .user-info {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border-left: 4px solid #667eea;
        }

        .user-info label {
            font-weight: 600;
            color: #495057;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .user-info .form-control {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 10px 15px;
            font-weight: 500;
        }

        .verification-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .verified {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
        }

        .unverified {
            background: linear-gradient(135deg, #ff6b6b 0%, #ffa8a8 100%);
            color: white;
        }

        .level-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-left: 8px;
            display: inline-block;
        }

        .level-nl0 { background: linear-gradient(135deg, #6c757d 0%, #adb5bd 100%); color: white; }
        .level-nl1 { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; }
        .level-nl2 { background: linear-gradient(135deg, #007bff 0%, #6610f2 100%); color: white; }
        .level-nl3 { background: linear-gradient(135deg, #fd7e14 0%, #ffc107 100%); color: white; }
        .level-nl4 { background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%); color: white; }

        .token-display {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px solid #dee2e6;
            border-radius: 12px;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
            word-break: break-all;
            position: relative;
            overflow: hidden;
        }

        .token-display::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .btn-custom {
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-logout {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            border: none;
            color: white;
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.3);
        }

        .btn-logout:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
            color: white;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- 用户信息卡片 -->
        <div class="card">
            <div class="card-header success">
                <h3 class="mb-0">
                    <i class="bi bi-person-check me-2"></i>用户信息
                </h3>
            </div>
            <div class="card-body">
                <?php if(!empty($_SESSION['user_picture'])): ?>
                    <div class="avatar-container">
                        <img src="<?php echo htmlspecialchars($_SESSION['user_picture']); ?>"
                             alt="用户头像"
                             class="avatar-img">
                    </div>
                <?php endif; ?>

                <div class="row g-3">
                    <div class="col-md-6">
                        <div class="user-info">
                            <label class="form-label">用户ID</label>
                            <input type="text" value="<?php echo htmlspecialchars($_SESSION['user_id'] ?? ''); ?>"
                                   class="form-control" readonly/>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="user-info">
                            <label class="form-label">
                                用户名
                                <?php
                                $levelBadge = getUserLevelBadge($_SESSION['user_groups'] ?? []);
                                if ($levelBadge):
                                ?>
                                    <span class="level-badge level-<?php echo strtolower($levelBadge); ?>"><?php echo $levelBadge; ?></span>
                                <?php endif; ?>
                            </label>
                            <input type="text" value="<?php echo htmlspecialchars($_SESSION['user_username'] ?? ''); ?>"
                                   class="form-control" readonly/>
                        </div>
                    </div>
                </div>

                <div class="row g-3">
                    <div class="col-md-6">
                        <div class="user-info">
                            <label class="form-label">显示名称</label>
                            <input type="text" value="<?php echo htmlspecialchars($_SESSION['user_name'] ?? ''); ?>"
                                   class="form-control" readonly/>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="user-info">
                            <label class="form-label">
                                邮箱
                                <?php if(isset($_SESSION['user_email_verified']) && $_SESSION['user_email_verified']): ?>
                                    <span class="verification-badge verified ms-2">已验证</span>
                                <?php else: ?>
                                    <span class="verification-badge unverified ms-2">未验证</span>
                                <?php endif; ?>
                            </label>
                            <input type="text" value="<?php echo htmlspecialchars($_SESSION['user_email'] ?? ''); ?>"
                                   class="form-control" readonly/>
                        </div>
                    </div>
                </div>

                <?php if(!empty($_SESSION['user_groups'])): ?>
                    <div class="user-info">
                        <label class="form-label">用户组</label>
                        <textarea class="form-control" rows="2" readonly><?php echo htmlspecialchars(json_encode($_SESSION['user_groups'], JSON_UNESCAPED_UNICODE)); ?></textarea>
                    </div>
                <?php endif; ?>

                <div class="text-center mt-4">
                    <a href="../index.php" class="btn btn-primary btn-custom me-2">
                        <i class="bi bi-house me-2"></i>进入系统
                    </a>
                    <a href="logout.php" class="btn btn-logout btn-custom">
                        <i class="bi bi-box-arrow-right me-2"></i>退出登录
                    </a>
                </div>
            </div>
        </div>

        <!-- 令牌信息卡片 -->
        <div class="card">
            <div class="card-header info">
                <h5 class="mb-0">
                    <i class="bi bi-key me-2"></i>令牌信息
                </h5>
            </div>
            <div class="card-body">
                <div class="user-info">
                    <label class="form-label">访问令牌 (Access Token)</label>
                    <div class="token-display"><?php echo htmlspecialchars($_SESSION['access_token'] ?? ''); ?></div>
                </div>

                <?php if(!empty($_SESSION['refresh_token'])): ?>
                    <div class="user-info">
                        <label class="form-label">刷新令牌 (Refresh Token)</label>
                        <div class="token-display"><?php echo htmlspecialchars($_SESSION['refresh_token']); ?></div>
                    </div>
                <?php endif; ?>

                <?php if(!empty($_SESSION['id_token'])): ?>
                    <div class="user-info">
                        <label class="form-label">ID令牌 (ID Token)</label>
                        <div class="token-display"><?php echo htmlspecialchars($_SESSION['id_token']); ?></div>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <?php if(!empty($_SESSION['id_token_data'])): ?>
            <!-- ID Token 解析数据 -->
            <div class="card">
                <div class="card-header warning">
                    <h5 class="mb-0">
                        <i class="bi bi-code-square me-2"></i>ID Token 解析数据
                    </h5>
                </div>
                <div class="card-body">
                    <div class="token-display">
                        <pre class="mb-0"><?php echo htmlspecialchars(json_encode($_SESSION['id_token_data'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)); ?></pre>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
