# 本地快速构建脚本
param(
    [switch]$Run,      # 构建后直接运行
    [switch]$Clean,    # 清理后构建
    [switch]$Verbose   # 详细输出
)

Write-Host "=== 本地快速构建 ===" -ForegroundColor Green

# 清理选项
if ($Clean) {
    Write-Host "清理旧文件..." -ForegroundColor Yellow
    Remove-Item "new-api.exe" -ErrorAction SilentlyContinue
    Remove-Item "*.exe" -ErrorAction SilentlyContinue
    go clean -cache
    Write-Host "✓ 清理完成" -ForegroundColor Green
}

# 设置环境
$env:GOPROXY = "https://goproxy.cn,direct"
$env:GOSUMDB = "sum.golang.google.cn"

# 确保web/dist存在
if (!(Test-Path "web/dist")) {
    New-Item -ItemType Directory -Path "web/dist" -Force | Out-Null
}
if (!(Test-Path "web/dist/index.html")) {
    '<html><head><title>New API</title></head><body><h1>New API Local Dev</h1></body></html>' | Out-File -FilePath "web/dist/index.html" -Encoding UTF8
}

# 确保VERSION文件
if (!(Test-Path "VERSION") -or (Get-Content "VERSION" -Raw).Trim() -eq "") {
    "v1.0.0-local" | Out-File -FilePath "VERSION" -Encoding UTF8 -NoNewline
}

$version = (Get-Content "VERSION" -Raw).Trim()
Write-Host "构建版本: $version" -ForegroundColor Cyan

# 构建选项
$buildArgs = @()
if ($Verbose) {
    $buildArgs += "-v"
}

# 尝试不同的构建方式
Write-Host "`n尝试构建..." -ForegroundColor Yellow

# 方式1: 基本构建
Write-Host "方式1: 基本构建" -ForegroundColor Cyan
try {
    $output = go build @buildArgs -o new-api-basic.exe . 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ 基本构建成功" -ForegroundColor Green
        $basicSuccess = $true
    } else {
        Write-Host "✗ 基本构建失败" -ForegroundColor Red
        if ($Verbose) { Write-Host $output -ForegroundColor Gray }
        $basicSuccess = $false
    }
} catch {
    Write-Host "✗ 基本构建异常: $_" -ForegroundColor Red
    $basicSuccess = $false
}

# 方式2: 带版本信息构建
if ($basicSuccess) {
    Write-Host "方式2: 带版本信息构建" -ForegroundColor Cyan
    try {
        $ldflags = "-X 'one-api/common.Version=$version'"
        $output = go build @buildArgs -ldflags $ldflags -o new-api-version.exe . 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ 版本构建成功" -ForegroundColor Green
            $versionSuccess = $true
        } else {
            Write-Host "✗ 版本构建失败" -ForegroundColor Red
            if ($Verbose) { Write-Host $output -ForegroundColor Gray }
            $versionSuccess = $false
        }
    } catch {
        Write-Host "✗ 版本构建异常: $_" -ForegroundColor Red
        $versionSuccess = $false
    }
}

# 方式3: 完整构建（优化）
if ($versionSuccess) {
    Write-Host "方式3: 完整优化构建" -ForegroundColor Cyan
    try {
        $ldflags = "-s -w -X 'one-api/common.Version=$version'"
        $output = go build @buildArgs -ldflags $ldflags -o new-api.exe . 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ 完整构建成功" -ForegroundColor Green
            $fullSuccess = $true
        } else {
            Write-Host "✗ 完整构建失败" -ForegroundColor Red
            if ($Verbose) { Write-Host $output -ForegroundColor Gray }
            $fullSuccess = $false
        }
    } catch {
        Write-Host "✗ 完整构建异常: $_" -ForegroundColor Red
        $fullSuccess = $false
    }
}

# 显示结果
Write-Host "`n=== 构建结果 ===" -ForegroundColor Green
$builtFiles = Get-ChildItem "new-api*.exe" -ErrorAction SilentlyContinue
if ($builtFiles) {
    foreach ($file in $builtFiles) {
        $sizeMB = [math]::Round($file.Length / 1MB, 2)
        Write-Host "✓ $($file.Name) - $sizeMB MB" -ForegroundColor Green
    }
    
    # 选择最佳版本
    $bestFile = "new-api.exe"
    if (!(Test-Path $bestFile)) {
        $bestFile = "new-api-version.exe"
        if (!(Test-Path $bestFile)) {
            $bestFile = "new-api-basic.exe"
        }
    }
    
    if (Test-Path $bestFile) {
        Write-Host "`n推荐使用: $bestFile" -ForegroundColor Cyan
        
        # 运行选项
        if ($Run) {
            Write-Host "`n启动应用..." -ForegroundColor Yellow
            Write-Host "访问地址: http://localhost:3000" -ForegroundColor Cyan
            Write-Host "按 Ctrl+C 停止" -ForegroundColor Yellow
            & ".\$bestFile"
        } else {
            Write-Host "`n要运行应用，使用:" -ForegroundColor Yellow
            Write-Host ".\$bestFile" -ForegroundColor Cyan
            Write-Host "或者: .\build-local.ps1 -Run" -ForegroundColor Cyan
        }
    }
} else {
    Write-Host "✗ 没有成功构建任何文件" -ForegroundColor Red
    Write-Host "`n诊断信息:" -ForegroundColor Yellow
    Write-Host "Go版本: $(go version)" -ForegroundColor Gray
    Write-Host "当前目录: $(Get-Location)" -ForegroundColor Gray
    Write-Host "go.mod存在: $(Test-Path 'go.mod')" -ForegroundColor Gray
    Write-Host "main.go存在: $(Test-Path 'main.go')" -ForegroundColor Gray
    Write-Host "web/dist存在: $(Test-Path 'web/dist')" -ForegroundColor Gray
}

# 清理临时文件
if ($builtFiles.Count -gt 1) {
    Write-Host "`n清理临时构建文件..." -ForegroundColor Yellow
    Remove-Item "new-api-basic.exe" -ErrorAction SilentlyContinue
    Remove-Item "new-api-version.exe" -ErrorAction SilentlyContinue
}

Write-Host "`n=== 构建完成 ===" -ForegroundColor Green
