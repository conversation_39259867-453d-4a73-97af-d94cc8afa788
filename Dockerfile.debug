# Debug Dockerfile to identify build issues
FROM golang:1.23.4-alpine AS builder

ENV GO111MODULE=on \
    CGO_ENABLED=0 \
    GOOS=linux \
    GOARCH=amd64 \
    GOPROXY=https://goproxy.cn,https://goproxy.io,https://proxy.golang.org,direct \
    GOSUMDB=off

WORKDIR /build

# Install necessary packages
RUN apk add --no-cache git ca-certificates tzdata curl

# Create minimal web/dist for Go embed first
RUN echo "Creating minimal web/dist structure..." && \
    mkdir -p web/dist && \
    echo '<!DOCTYPE html><html><head><title>New API Debug</title></head><body><h1>Debug Mode</h1></body></html>' > web/dist/index.html && \
    echo "web/dist created successfully"

# Copy and verify go.mod/go.sum
COPY go.mod go.sum ./
RUN echo "=== Go Module Files ===" && \
    echo "go.mod content:" && head -20 go.mod && \
    echo "go.sum exists: $(test -f go.sum && echo 'YES' || echo 'NO')" && \
    echo "========================"

# Download dependencies with detailed logging
RUN echo "Downloading Go dependencies..." && \
    go mod download -x 2>&1 && \
    echo "Dependencies downloaded successfully"

# Copy VERSION file and verify
COPY VERSION ./
RUN echo "VERSION file content: '$(cat VERSION)'" && \
    if [ ! -s VERSION ]; then \
        echo "VERSION file is empty, creating default..." && \
        echo "v1.0.0" > VERSION; \
    fi

# Copy main.go first to test basic compilation
COPY main.go ./
RUN echo "Testing main.go compilation..." && \
    go build -n main.go 2>&1 | head -10 && \
    echo "Main.go syntax check passed"

# Copy source directories one by one to identify problematic files
COPY common/ ./common/
RUN echo "Testing with common/ directory..." && \
    go build -n . 2>&1 | head -5 || echo "Issue with common directory"

COPY constant/ ./constant/
RUN echo "Testing with constant/ directory..." && \
    go build -n . 2>&1 | head -5 || echo "Issue with constant directory"

COPY controller/ ./controller/
RUN echo "Testing with controller/ directory..." && \
    go build -n . 2>&1 | head -5 || echo "Issue with controller directory"

COPY dto/ ./dto/
COPY middleware/ ./middleware/
COPY model/ ./model/
COPY relay/ ./relay/
COPY router/ ./router/
COPY service/ ./service/
COPY setting/ ./setting/
COPY types/ ./types/
COPY i18n/ ./i18n/

# Final verification
RUN echo "=== Final Build Environment ===" && \
    echo "All files copied, checking structure:" && \
    find . -name "*.go" | wc -l && echo "Go files found" && \
    echo "web/dist contents:" && ls -la web/dist/ && \
    echo "VERSION: $(cat VERSION)" && \
    echo "================================"

# Test go vet
RUN echo "Running go vet..." && \
    go vet ./... 2>&1 || echo "Go vet completed with warnings"

# Test compilation step by step
RUN echo "Testing compilation..." && \
    echo "Step 1: Check imports" && \
    go list -f '{{.ImportPath}}: {{.Imports}}' . | head -5 && \
    echo "Step 2: Check dependencies" && \
    go list -m all | head -10 && \
    echo "Step 3: Attempt build" && \
    go build -v -ldflags "-s -w -X 'one-api/common.Version=$(cat VERSION)'" -o one-api 2>&1

# Verify binary
RUN if [ -f one-api ]; then \
        echo "Build successful! Binary info:" && \
        ls -la one-api && file one-api; \
    else \
        echo "Build failed - no binary created" && \
        exit 1; \
    fi

FROM alpine:latest
RUN apk add --no-cache ca-certificates tzdata
COPY --from=builder /build/one-api /usr/local/bin/one-api
RUN chmod +x /usr/local/bin/one-api
EXPOSE 3000
ENTRYPOINT ["/usr/local/bin/one-api"]
