# Docker构建问题排查指南

## 问题描述
构建过程中出现错误：
```
ERROR: failed to build: failed to solve: process "/bin/sh -c go build -ldflags \"-s -w -X 'one-api/common.Version=$(cat VERSION)'\" -o one-api" did not complete successfully: exit code: 1
```

## 主要问题原因

### 1. 网络连接问题 🌐
**症状**: Go模块下载失败，出现连接超时错误
**原因**: 无法访问 `proxy.golang.org` 或网络连接不稳定

**解决方案**:
- 已在Dockerfile中添加中国Go代理：
  ```dockerfile
  ENV GOPROXY=https://goproxy.cn,https://goproxy.io,direct \
      GOSUMDB=sum.golang.google.cn
  ```

### 2. 前端构建问题 🎨
**症状**: web/dist目录不存在或为空
**原因**: 前端构建失败或未正确复制

**解决方案**:
- 确保bun安装和构建成功
- 检查web目录结构完整性

### 3. 依赖版本冲突 📦
**症状**: Go模块版本不兼容
**原因**: go.mod中的依赖版本过旧或冲突

## 修复后的Dockerfile特性

### ✅ 网络优化
- 使用多个Go代理源
- 设置中国镜像源加速下载
- 添加详细的构建日志

### ✅ 构建优化
- 分阶段构建减少镜像大小
- 添加构建验证步骤
- 改进错误处理和调试信息

### ✅ 安全优化
- 使用非root用户运行
- 最小化运行时镜像
- 只复制必要文件

## 使用方法

### Windows (PowerShell)
```powershell
.\build-docker.ps1
```

### Linux/Mac (Bash)
```bash
chmod +x build-docker.sh
./build-docker.sh
```

### 手动构建
```bash
# 设置Go代理
export GOPROXY="https://goproxy.cn,https://goproxy.io,direct"
export GOSUMDB="sum.golang.google.cn"

# 构建镜像
docker build -t new-api:latest .
```

## 常见问题排查

### Q1: Docker构建卡在依赖下载
**A**: 网络问题，使用中国镜像源：
```bash
export GOPROXY="https://goproxy.cn,direct"
```

### Q2: 前端构建失败
**A**: 检查web目录和bun配置：
```bash
cd web
bun install
bun run build
```

### Q3: 版本信息错误
**A**: 确保VERSION文件存在且有内容：
```bash
echo "v1.0.0" > VERSION
```

### Q4: 权限问题
**A**: 确保Docker有足够权限：
```bash
sudo docker build -t new-api:latest .
```

## 验证构建成功

构建成功后，运行容器测试：
```bash
# 运行容器
docker run -p 3000:3000 new-api:latest

# 检查容器状态
docker ps

# 测试API
curl http://localhost:3000
```

## 进一步优化建议

1. **使用Docker BuildKit**: 启用并行构建
   ```bash
   DOCKER_BUILDKIT=1 docker build -t new-api:latest .
   ```

2. **使用多阶段缓存**: 缓存依赖层
   ```dockerfile
   # 在Dockerfile中已实现
   ```

3. **网络代理**: 如果仍有网络问题，考虑使用代理
   ```bash
   docker build --build-arg HTTP_PROXY=http://proxy:port -t new-api:latest .
   ```

## 联系支持

如果问题仍然存在，请提供：
1. 完整的错误日志
2. Docker版本信息
3. 网络环境信息
4. 操作系统信息
