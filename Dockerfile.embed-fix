# Dockerfile with proper embed handling
FROM golang:1.23.4-alpine AS builder

ENV GO111MODULE=on \
    CGO_ENABLED=0 \
    GOOS=linux \
    GOARCH=amd64 \
    GOPROXY=https://goproxy.cn,direct \
    GOSUMDB=off

WORKDIR /build

# Install packages
RUN apk add --no-cache git ca-certificates tzdata

# CRITICAL: Create web/dist BEFORE copying any Go files
RUN echo "Creating web/dist structure..." && \
    mkdir -p web/dist && \
    echo '<!DOCTYPE html><html><head><title>New API</title><meta charset="UTF-8"></head><body><h1>New API</h1><p>Application is running</p></body></html>' > web/dist/index.html && \
    echo "web/dist structure created" && \
    ls -la web/dist/

# Copy go.mod and go.sum first
COPY go.mod go.sum ./
RUN echo "Downloading Go modules..." && \
    go mod download && \
    echo "Go modules downloaded"

# Copy VERSION file
COPY VERSION ./
RUN echo "VERSION content: $(cat VERSION)" && \
    if [ ! -s VERSION ]; then echo "v1.0.0" > VERSION; fi

# Now copy ALL source files at once to maintain proper structure
COPY . .

# CRITICAL: Verify web/dist still exists after COPY . .
RUN echo "Verifying web/dist after copy..." && \
    ls -la web/dist/ && \
    echo "web/dist verification complete"

# Test embed paths specifically
RUN echo "Testing embed paths..." && \
    test -f web/dist/index.html && echo "✓ web/dist/index.html exists" || echo "✗ web/dist/index.html missing" && \
    test -d web/dist && echo "✓ web/dist directory exists" || echo "✗ web/dist directory missing"

# Try build without ldflags first
RUN echo "Testing basic build..." && \
    go build -o one-api-basic . && \
    echo "Basic build successful" && \
    ls -la one-api-basic

# Try build with ldflags
RUN echo "Building with ldflags..." && \
    go build -ldflags "-s -w -X 'one-api/common.Version=$(cat VERSION)'" -o one-api . && \
    echo "Full build successful"

# Verify final binary
RUN echo "Build verification:" && \
    ls -la one-api* && \
    file one-api && \
    echo "Build completed successfully!"

FROM alpine:latest

RUN apk upgrade --no-cache \
    && apk add --no-cache ca-certificates tzdata ffmpeg \
    && update-ca-certificates \
    && adduser -D -s /bin/sh oneapi

COPY --from=builder /build/one-api /usr/local/bin/one-api
RUN chmod +x /usr/local/bin/one-api

USER oneapi
EXPOSE 3000
WORKDIR /data
ENTRYPOINT ["/usr/local/bin/one-api"]
