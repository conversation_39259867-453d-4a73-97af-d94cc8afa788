# Simplified Dockerfile with step-by-step error checking
FROM golang:1.23.4-alpine AS builder

ENV GO111MODULE=on \
    CGO_ENABLED=0 \
    GOOS=linux \
    GOARCH=amd64 \
    GOPROXY=https://goproxy.cn,direct \
    GOSUMDB=off

WORKDIR /build

# Install packages
RUN apk add --no-cache git ca-certificates tzdata

# Create web/dist first (critical for Go embed)
RUN mkdir -p web/dist && \
    echo '<!DOCTYPE html><html><head><title>New API</title></head><body><h1>New API</h1></body></html>' > web/dist/index.html

# Copy and download dependencies
COPY go.mod go.sum ./
RUN go mod download

# Copy VERSION
COPY VERSION ./
RUN cat VERSION || echo "v1.0.0" > VERSION

# Copy main.go and test
COPY main.go ./
RUN echo "Testing main.go..." && go build -n main.go 2>/dev/null || echo "Main.go check completed"

# Copy all source files
COPY . .

# Ensure web/dist is still there after copying
RUN ls -la web/dist/

# Simple build without complex ldflags first
RUN echo "Simple build test..." && \
    go build -o one-api-simple .

# Now try with ldflags
RUN echo "Building with ldflags..." && \
    go build -ldflags "-s -w -X 'one-api/common.Version=$(cat VERSION)'" -o one-api .

# Verify
RUN ls -la one-api* && echo "Build successful!"

FROM alpine:latest
RUN apk add --no-cache ca-certificates tzdata
COPY --from=builder /build/one-api /usr/local/bin/one-api
RUN chmod +x /usr/local/bin/one-api
EXPOSE 3000
ENTRYPOINT ["/usr/local/bin/one-api"]
