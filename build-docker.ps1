# Docker构建脚本 - 解决网络问题
# PowerShell script to build Docker image with network fixes

Write-Host "=== New API Docker Build Script ===" -ForegroundColor Green

# 设置Go代理环境变量
Write-Host "Setting Go proxy environment variables..." -ForegroundColor Yellow
$env:GOPROXY = "https://goproxy.cn,https://goproxy.io,direct"
$env:GOSUMDB = "sum.golang.google.cn"

# 检查Docker是否运行
Write-Host "Checking Docker status..." -ForegroundColor Yellow
try {
    docker version | Out-Null
    Write-Host "Docker is running ✓" -ForegroundColor Green
} catch {
    Write-Host "Docker is not running or not accessible ✗" -ForegroundColor Red
    Write-Host "Please start Docker Desktop and try again." -ForegroundColor Red
    exit 1
}

# 检查必要文件
Write-Host "Checking required files..." -ForegroundColor Yellow
$requiredFiles = @("Dockerfile", "go.mod", "go.sum", "VERSION", "main.go")
foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Host "  $file ✓" -ForegroundColor Green
    } else {
        Write-Host "  $file ✗" -ForegroundColor Red
        exit 1
    }
}

# 检查web目录
if (Test-Path "web") {
    Write-Host "  web/ directory ✓" -ForegroundColor Green
} else {
    Write-Host "  web/ directory ✗" -ForegroundColor Red
    exit 1
}

# 构建Docker镜像
Write-Host "Building Docker image..." -ForegroundColor Yellow
$imageName = "new-api:latest"

try {
    docker build -t $imageName . --progress=plain
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Docker build completed successfully! ✓" -ForegroundColor Green
        Write-Host "Image name: $imageName" -ForegroundColor Cyan
        
        # 显示镜像信息
        Write-Host "`nImage information:" -ForegroundColor Yellow
        docker images $imageName
        
        Write-Host "`nTo run the container:" -ForegroundColor Yellow
        Write-Host "docker run -p 3000:3000 $imageName" -ForegroundColor Cyan
    } else {
        Write-Host "Docker build failed ✗" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "Error during Docker build: $_" -ForegroundColor Red
    exit 1
}

Write-Host "`n=== Build Complete ===" -ForegroundColor Green
