# Comprehensive build diagnosis script
Write-Host "=== Docker Build Diagnosis ===" -ForegroundColor Green

# 准备环境
Write-Host "Preparing test environment..." -ForegroundColor Yellow
if (!(Test-Path "web/dist")) {
    New-Item -ItemType Directory -Path "web/dist" -Force | Out-Null
}
'<!DOCTYPE html><html><head><title>Test</title></head><body><h1>Test</h1></body></html>' | Out-File -FilePath "web/dist/index.html" -Encoding UTF8 -Force

# 检查关键文件
Write-Host "`nChecking critical files..." -ForegroundColor Yellow
$criticalFiles = @("go.mod", "go.sum", "main.go", "VERSION")
foreach ($file in $criticalFiles) {
    if (Test-Path $file) {
        $size = (Get-Item $file).Length
        Write-Host "✓ $file ($size bytes)" -ForegroundColor Green
    } else {
        Write-Host "✗ $file missing" -ForegroundColor Red
    }
}

# 检查VERSION文件内容
if (Test-Path "VERSION") {
    $versionContent = Get-Content "VERSION" -Raw
    Write-Host "VERSION content: '$versionContent'" -ForegroundColor Cyan
    if ([string]::IsNullOrWhiteSpace($versionContent)) {
        Write-Host "VERSION file is empty, creating default..." -ForegroundColor Yellow
        "v1.0.0" | Out-File -FilePath "VERSION" -Encoding UTF8 -NoNewline
    }
}

# 本地Go测试（如果可用）
Write-Host "`nTesting local Go environment..." -ForegroundColor Yellow
try {
    $goVersion = go version 2>&1
    Write-Host "Local Go: $goVersion" -ForegroundColor Green
    
    # 测试本地构建
    Write-Host "Testing local build..." -ForegroundColor Cyan
    $buildResult = go build -o test-local.exe . 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Local build successful!" -ForegroundColor Green
        Remove-Item "test-local.exe" -ErrorAction SilentlyContinue
    } else {
        Write-Host "✗ Local build failed:" -ForegroundColor Red
        Write-Host $buildResult -ForegroundColor Yellow
    }
} catch {
    Write-Host "Go not available locally" -ForegroundColor Yellow
}

# Docker诊断构建
Write-Host "`nRunning Docker diagnostic build..." -ForegroundColor Yellow
Write-Host "This will show exactly where the build fails..." -ForegroundColor Cyan

try {
    # 运行详细诊断
    docker build -f Dockerfile.minimal-test -t new-api:diagnosis . --progress=plain --no-cache
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "`n✓ Diagnostic build completed!" -ForegroundColor Green
        Write-Host "Check the output above to see which test passed/failed" -ForegroundColor Cyan
    } else {
        Write-Host "`n✗ Diagnostic build failed" -ForegroundColor Red
        Write-Host "Check the output above to see exactly where it failed" -ForegroundColor Yellow
    }
} catch {
    Write-Host "Error running diagnostic: $_" -ForegroundColor Red
}

# 尝试简化的构建
Write-Host "`nTrying ultra-simple build..." -ForegroundColor Yellow

# 创建超简单的Dockerfile
$simpleDockerfile = @'
FROM golang:1.23.4-alpine
WORKDIR /app
RUN apk add --no-cache git ca-certificates
RUN mkdir -p web/dist && echo '<html><body>Test</body></html>' > web/dist/index.html
COPY go.mod go.sum ./
RUN go mod download
COPY . .
RUN go build -o app .
RUN ls -la app
FROM alpine
COPY --from=0 /app/app /app
ENTRYPOINT ["/app"]
'@

$simpleDockerfile | Out-File -FilePath "Dockerfile.ultra-simple" -Encoding UTF8

try {
    docker build -f Dockerfile.ultra-simple -t new-api:ultra-simple . --no-cache
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Ultra-simple build successful!" -ForegroundColor Green
        
        # 测试运行
        Write-Host "Testing ultra-simple container..." -ForegroundColor Cyan
        $containerId = docker run -d new-api:ultra-simple
        Start-Sleep 2
        docker logs $containerId
        docker stop $containerId | Out-Null
        docker rm $containerId | Out-Null
        
    } else {
        Write-Host "✗ Even ultra-simple build failed" -ForegroundColor Red
    }
} catch {
    Write-Host "Error with ultra-simple build: $_" -ForegroundColor Red
}

Write-Host "`n=== Diagnosis Summary ===" -ForegroundColor Green
Write-Host "1. Check the diagnostic output above for specific failure points" -ForegroundColor Cyan
Write-Host "2. If local Go build works but Docker fails, it's an environment issue" -ForegroundColor Cyan
Write-Host "3. If ultra-simple build works, the issue is in the complex build steps" -ForegroundColor Cyan
Write-Host "4. Look for specific error messages in the Docker output" -ForegroundColor Cyan

# 清理
Remove-Item "Dockerfile.ultra-simple" -ErrorAction SilentlyContinue
