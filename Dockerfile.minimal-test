# Minimal test to identify exact build issue
FROM golang:1.23.4-alpine AS builder

ENV GO111MODULE=on \
    CGO_ENABLED=0 \
    GOOS=linux \
    GOARCH=amd64 \
    GOPROXY=https://goproxy.cn,direct \
    GOSUMDB=off

WORKDIR /build

# Install packages
RUN apk add --no-cache git ca-certificates tzdata

# Test 1: Create minimal web/dist
RUN echo "=== Test 1: Creating web/dist ===" && \
    mkdir -p web/dist && \
    echo '<!DOCTYPE html><html><head><title>Test</title></head><body><h1>Test</h1></body></html>' > web/dist/index.html && \
    ls -la web/dist/ && \
    echo "web/dist created successfully"

# Test 2: Copy and check go.mod
COPY go.mod go.sum ./
RUN echo "=== Test 2: Go modules ===" && \
    echo "go.mod first 10 lines:" && head -10 go.mod && \
    echo "Downloading modules..." && \
    go mod download && \
    echo "Modules downloaded successfully"

# Test 3: Copy VERSION and verify
COPY VERSION ./
RUN echo "=== Test 3: VERSION file ===" && \
    echo "VERSION content: '$(cat VERSION)'" && \
    echo "VERSION file size: $(wc -c < VERSION)" && \
    if [ ! -s VERSION ]; then echo "VERSION empty, creating default" && echo "v1.0.0" > VERSION; fi && \
    echo "Final VERSION: '$(cat VERSION)'"

# Test 4: Copy main.go only and test
COPY main.go ./
RUN echo "=== Test 4: Testing main.go only ===" && \
    echo "main.go size: $(wc -l < main.go)" && \
    echo "Testing main.go compilation..." && \
    go build -n main.go 2>&1 | head -5 && \
    echo "main.go test completed"

# Test 5: Copy common package (most likely to have issues)
COPY common/ ./common/
RUN echo "=== Test 5: Testing with common package ===" && \
    echo "common package files: $(find common -name '*.go' | wc -l)" && \
    go list ./common && \
    echo "common package test completed"

# Test 6: Copy remaining packages one by one
COPY constant/ ./constant/
COPY dto/ ./dto/
COPY types/ ./types/
RUN echo "=== Test 6: Basic packages copied ===" && \
    go list ./... | head -10 && \
    echo "Basic packages test completed"

# Test 7: Copy remaining source
COPY controller/ ./controller/
COPY middleware/ ./middleware/
COPY model/ ./model/
COPY relay/ ./relay/
COPY router/ ./router/
COPY service/ ./service/
COPY setting/ ./setting/
COPY i18n/ ./i18n/

# Test 8: Final structure check
RUN echo "=== Test 8: Final structure ===" && \
    echo "Total Go files: $(find . -name '*.go' | wc -l)" && \
    echo "All packages:" && go list ./... && \
    echo "Structure check completed"

# Test 9: Try different build approaches
RUN echo "=== Test 9: Build attempts ===" && \
    echo "Attempt 1: Basic build" && \
    (go build . 2>&1 || echo "Basic build failed") && \
    echo "Attempt 2: With output file" && \
    (go build -o test-app . 2>&1 || echo "Output build failed") && \
    echo "Attempt 3: With simple ldflags" && \
    (go build -ldflags "-s -w" -o test-app2 . 2>&1 || echo "Simple ldflags failed") && \
    echo "Attempt 4: With version ldflags" && \
    (go build -ldflags "-s -w -X 'one-api/common.Version=v1.0.0'" -o test-app3 . 2>&1 || echo "Version ldflags failed") && \
    echo "Build attempts completed"

# Test 10: Show any successful builds
RUN echo "=== Test 10: Results ===" && \
    ls -la test-app* 2>/dev/null || echo "No successful builds" && \
    echo "Test completed"

FROM alpine:latest
RUN echo "If you see this, the build stage completed successfully"
