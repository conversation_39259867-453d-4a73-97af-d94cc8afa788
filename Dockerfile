FROM oven/bun:latest AS builder

WORKDIR /build
COPY web/package.json web/bun.lock ./
RUN bun install --frozen-lockfile
COPY ./web .
COPY ./VERSION .
RUN DISABLE_ESLINT_PLUGIN='true' VITE_REACT_APP_VERSION=$(cat VERSION) bun run build || \
    (echo "Frontend build failed, creating fallback..." && \
     mkdir -p dist && \
     echo '<!DOCTYPE html><html><head><title>New API</title></head><body><h1>New API</h1><p>Frontend build failed</p></body></html>' > dist/index.html)

FROM golang:1.23.4-alpine AS builder2

ENV GO111MODULE=on \
    CGO_ENABLED=0 \
    GOOS=linux \
    GOARCH=amd64 \
    GOPROXY=https://goproxy.cn,https://goproxy.io,https://proxy.golang.org,direct \
    GOSUMDB=off

WORKDIR /build

# Install necessary packages
RUN apk add --no-cache git ca-certificates tzdata curl

# Copy go mod files and download dependencies with retry
COPY go.mod go.sum ./
RUN echo "Downloading dependencies with retries..." && \
    for i in 1 2 3; do \
        echo "Attempt $i..." && \
        go mod download && break || \
        (echo "Attempt $i failed, retrying..." && sleep 5); \
    done && \
    echo "Dependencies downloaded successfully"

# Copy source files (excluding web directory to avoid conflicts)
COPY *.go ./
COPY VERSION ./
COPY common/ ./common/
COPY constant/ ./constant/
COPY controller/ ./controller/
COPY dto/ ./dto/
COPY middleware/ ./middleware/
COPY model/ ./model/
COPY relay/ ./relay/
COPY router/ ./router/
COPY service/ ./service/
COPY setting/ ./setting/
COPY types/ ./types/
COPY i18n/ ./i18n/

# Copy frontend build from first stage
COPY --from=builder /build/dist ./web/dist

# Ensure VERSION file exists and has content
RUN if [ ! -s VERSION ]; then echo "v1.0.0" > VERSION; fi

# Ensure web/dist directory exists and has required files for Go embed
RUN if [ ! -f web/dist/index.html ]; then \
        echo "Creating fallback index.html for Go embed..." && \
        mkdir -p web/dist && \
        echo '<!DOCTYPE html><html><head><title>New API</title></head><body><h1>New API</h1><p>Backend only mode</p></body></html>' > web/dist/index.html; \
    fi

# Verify build requirements
RUN echo "=== Build Information ===" && \
    echo "Go version: $(go version)" && \
    echo "VERSION file content: $(cat VERSION)" && \
    echo "Web dist files: $(ls -la web/dist/ | wc -l) files" && \
    echo "========================="

# Build the application with error handling
RUN echo "Building application..." && \
    echo "Go version: $(go version)" && \
    echo "GOPROXY: $GOPROXY" && \
    echo "Building with version: $(cat VERSION)" && \
    echo "Checking Go syntax..." && \
    (go vet ./... || echo "Warning: go vet found issues, continuing...") && \
    echo "Starting build..." && \
    go build -ldflags "-s -w -X 'one-api/common.Version=$(cat VERSION)'" -o one-api 2>&1 && \
    echo "Build completed successfully!"

# Verify the binary was created and is executable
RUN ls -la one-api && file one-api && echo "Binary verification passed!"

FROM alpine:latest

RUN apk upgrade --no-cache \
    && apk add --no-cache ca-certificates tzdata ffmpeg \
    && update-ca-certificates \
    && adduser -D -s /bin/sh oneapi

COPY --from=builder2 /build/one-api /usr/local/bin/one-api
RUN chmod +x /usr/local/bin/one-api

USER oneapi
EXPOSE 3000
WORKDIR /data
ENTRYPOINT ["/usr/local/bin/one-api"]
