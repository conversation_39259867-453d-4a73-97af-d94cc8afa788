FROM oven/bun:latest AS builder

WORKDIR /build
COPY web/package.json web/bun.lock ./
RUN bun install --frozen-lockfile
COPY ./web .
COPY ./VERSION .
RUN DISABLE_ESLINT_PLUGIN='true' VITE_REACT_APP_VERSION=$(cat VERSION) bun run build

FROM golang:1.23-alpine AS builder2

ENV GO111MODULE=on \
    CGO_ENABLED=0 \
    GOOS=linux \
    GOARCH=amd64 \
    GOPROXY=https://goproxy.cn,https://goproxy.io,direct \
    GOSUMDB=sum.golang.google.cn

WORKDIR /build

# Install necessary packages
RUN apk add --no-cache git ca-certificates tzdata

# Copy go mod files and download dependencies first
COPY go.mod go.sum ./
RUN go mod download && go mod verify

# Copy all source files
COPY . .

# Copy frontend build from first stage
COPY --from=builder /build/dist ./web/dist

# Ensure VERSION file exists and has content
RUN if [ ! -s VERSION ]; then echo "v1.0.0" > VERSION; fi

# Verify build requirements
RUN echo "=== Build Information ===" && \
    echo "Go version: $(go version)" && \
    echo "VERSION file content: $(cat VERSION)" && \
    echo "Web dist files: $(ls -la web/dist/ | wc -l) files" && \
    echo "========================="

# Build the application with detailed output
RUN echo "Building application..." && \
    go build -v -ldflags "-s -w -X 'one-api/common.Version=$(cat VERSION)'" -o one-api && \
    echo "Build completed successfully!"

# Verify the binary was created and is executable
RUN ls -la one-api && file one-api && echo "Binary verification passed!"

FROM alpine:latest

RUN apk upgrade --no-cache \
    && apk add --no-cache ca-certificates tzdata ffmpeg \
    && update-ca-certificates \
    && adduser -D -s /bin/sh oneapi

COPY --from=builder2 /build/one-api /usr/local/bin/one-api
RUN chmod +x /usr/local/bin/one-api

USER oneapi
EXPOSE 3000
WORKDIR /data
ENTRYPOINT ["/usr/local/bin/one-api"]
