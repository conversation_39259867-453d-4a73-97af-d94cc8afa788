<?php
/**
 * Nodeloc登录SDK
 * 1.0
 * https://nodeloc.cc/
**/

error_reporting(0);
session_start();
@header('Content-Type: text/html; charset=UTF-8');
include('config.php');

if($ClientId==''){
    die('请先在config.php中填入密钥');
}

if($RedirectUri==''){
    die('请先在config.php中填入回调地址');
}

// 生成随机state参数，用于防CSRF攻击
$state = bin2hex(random_bytes(16));
$_SESSION['oauth_state'] = $state;

// 构建授权URL - 根据ABOUT.md中的规范
$params = [
    'response_type' => 'code',
    'client_id' => $ClientId,
    'redirect_uri' => $RedirectUri,  // 使用配置的回调地址
    'scope' => 'openid profile',
    'state' => $state
];

$Oauth_url = 'https://conn.nodeloc.cc/oauth2/auth?' . http_build_query($params);

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>正在跳转到授权页面...</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            font-family: 'Microsoft YaHei', sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .loading-container {
            text-align: center;
            background: rgba(255, 255, 255, 0.95);
            padding: 3rem 2rem;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(15px);
            max-width: 450px;
            width: 100%;
        }

        .loading-icon {
            font-size: 4rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1.5rem;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .loading-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 1rem;
        }

        .loading-text {
            font-size: 1rem;
            color: #666;
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 2rem;
            position: relative;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            animation: progress 3s ease-out;
            border-radius: 10px;
        }

        .manual-link {
            display: inline-block;
            padding: 12px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .manual-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
            color: white;
            text-decoration: none;
        }

        .security-info {
            font-size: 0.9rem;
            color: #666;
            margin-top: 1.5rem;
            padding: 1rem;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 12px;
            border-left: 4px solid #667eea;
        }

        @keyframes progress {
            0% { width: 0%; }
            100% { width: 100%; }
        }

        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(102, 126, 234, 0.3);
            border-radius: 50%;
            border-top-color: #667eea;
            animation: spin 1s ease-in-out infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="loading-container">
        <div class="loading-icon">
            <i class="bi bi-shield-lock"></i>
        </div>

        <div class="loading-title">
            <span class="spinner"></span>
            正在跳转授权
        </div>

        <div class="loading-text">
            正在为您跳转到 Nodeloc 平台进行安全授权<br>
            请稍候...
        </div>

        <div class="progress-bar">
            <div class="progress-fill"></div>
        </div>

        <div class="security-info">
            <i class="bi bi-shield-check me-2"></i>
            <strong>安全提示：</strong>您将被重定向到 Nodeloc 官方授权页面<br>
            请在授权页面确认您的登录信息，确保账户安全
        </div>

        <div class="mt-4">
            <a href="<?php echo htmlspecialchars($Oauth_url); ?>" class="manual-link">
                <i class="bi bi-arrow-right me-2"></i>立即跳转
            </a>
        </div>

        <div class="mt-3">
            <a href="../index.php" class="text-muted text-decoration-none">
                <i class="bi bi-arrow-left me-1"></i>返回首页
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 3秒后自动跳转
        setTimeout(function() {
            window.location.href = '<?php echo addslashes($Oauth_url); ?>';
        }, 3000);

        // 倒计时显示
        let countdown = 3;
        const countdownElement = document.querySelector('.loading-title');
        const originalText = countdownElement.innerHTML;

        const timer = setInterval(function() {
            countdown--;
            if (countdown > 0) {
                countdownElement.innerHTML = `<span class="spinner"></span>正在跳转授权 (${countdown}s)`;
            } else {
                countdownElement.innerHTML = `<span class="spinner"></span>正在跳转...`;
                clearInterval(timer);
            }
        }, 1000);
    </script>
</body>
</html>
